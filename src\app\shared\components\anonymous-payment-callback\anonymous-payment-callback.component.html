<!-- Anonymous Payment Callback Page -->
<div class="payment-callback-container">
  <!-- Header -->
  <div class="callback-header">
    <div class="topheader">
      <a routerLink="/home" class="logo">
        <img src="assets/images/logo.png" alt="CabYaari" style="height: 48px; width: 175px;"/>
      </a>
    </div>
  </div>

  <!-- Main Content -->
  <div class="callback-content">
    <div class="status-container">
      <!-- Loading Spinner -->
      <div *ngIf="showSpinner" class="spinner-container">
        <div class="spinner-border" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>

      <!-- Status Icon -->
      <div *ngIf="!showSpinner" class="status-icon">
        <i class="fa fa-check-circle" 
           *ngIf="statusMessage.includes('Successful')" 
           style="color: #28a745; font-size: 3rem;"></i>
        <i class="fa fa-times-circle" 
           *ngIf="statusMessage.includes('Failed')" 
           style="color: #dc3545; font-size: 3rem;"></i>
        <i class="fa fa-ban" 
           *ngIf="statusMessage.includes('Cancelled')" 
           style="color: #6c757d; font-size: 3rem;"></i>
        <i class="fa fa-clock" 
           *ngIf="statusMessage.includes('Processing') || statusMessage.includes('Verifying')" 
           style="color: #ffc107; font-size: 3rem;"></i>
      </div>

      <!-- Status Message -->
      <div class="status-message">
        <h2>{{ statusMessage }}</h2>
        <p>{{ statusDescription }}</p>
      </div>

      <!-- Progress Info -->
      <div *ngIf="showProgressInfo" class="progress-info">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        <p class="progress-text">Please do not close this window or navigate away...</p>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons" *ngIf="!showSpinner && !showProgressInfo">
        <button class="btn btn-primary" (click)="navigateHome()">
          <i class="fa fa-home"></i> Go to Home
        </button>
      </div>
    </div>
  </div>

  <!-- Footer Info -->
  <div class="callback-footer">
    <div class="info-text">
      <p><i class="fa fa-info-circle"></i> 
         If you're experiencing issues, please contact our customer support.
      </p>
    </div>
  </div>
</div>
