import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AppConfig } from 'src/app/configs/app.config';
import { LoggerService } from '../interceptors/logger.service';
import { ProgressService } from './progress.service';
import {
  AnonymousBookingDetailsResponse,
  AnonymousPaymentRequest,
  AnonymousPaymentTokenResponse,
  AnonymousPaymentVerifyRequest,
  AnonymousPaymentVerifyResponse,
  AnonymousReceiptResponse
} from '../models/anonymous-payment.model';

declare global {
  interface Window {
    PhonePeCheckout: {
      transact: (options: { tokenUrl: string; callback: (response: string) => void; type: string }) => void;
      closePage: () => void;
    };
    phonepeScriptLoaded: boolean;
  }
}

@Injectable({
  providedIn: 'root'
})
export class AnonymousPaymentService {
  private logger: LoggerService;
  private readonly API_BASE = `${AppConfig.CabYaari_WebAPI_New}/api/v1/anonymous-payments`;
  
  // Subject to track payment completion for anonymous users
  private paymentCompletedSubject = new BehaviorSubject<boolean>(false);
  public paymentCompleted$ = this.paymentCompletedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private toastrService: ToastrService,
    private progressService: ProgressService
  ) {
    this.logger = LoggerService.createLogger('AnonymousPaymentService');
  }

  private getHttpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
        // No Authorization header for anonymous requests
      })
    };
  }

  /**
   * Get booking details by ID for anonymous payment
   */
  getBookingDetails(bookingId: string): Observable<AnonymousBookingDetailsResponse> {
    this.logger.debug('Getting anonymous booking details for ID:', bookingId);
    const url = `${this.API_BASE}/booking/${bookingId}`;
    return this.http.get<AnonymousBookingDetailsResponse>(url, this.getHttpOptions());
  }



  /**
   * Initiate anonymous payment
   */
  initiatePayment(request: AnonymousPaymentRequest): Observable<AnonymousPaymentTokenResponse> {
    this.logger.debug('Initiating anonymous payment:', request);
    const url = `${this.API_BASE}/initiate`;
    return this.http.post<AnonymousPaymentTokenResponse>(url, request, this.getHttpOptions());
  }

  /**
   * Start PhonePe payment flow for anonymous user
   */
  startPhonePePayment(bookingId: string, paymentOption: number, amountToPay: number): void {
    this.showProgressBar();
    
    const paymentRequest: AnonymousPaymentRequest = {
      bookingId: bookingId,
      paymentOption: paymentOption,
      amountToPay: amountToPay,
      callbackUrl: `${window.location.origin}/#/anonymous-payment-callback`
    };

    this.initiatePayment(paymentRequest).subscribe(
      (tokenResponse) => {
        if (!tokenResponse.succeeded) {
          this.logger.error('Failed to get PhonePe token:', tokenResponse.message);
          this.showPaymentFailedMessage();
          this.hideProgressBar();
          return;
        }

        // Store transaction data for callback handling
        const transactionData = {
          merchantTransactionId: tokenResponse.data.merchantTransactionId,
          orderId: tokenResponse.data.orderId,
          bookingId: tokenResponse.data.bookingId,
          timestamp: Date.now(),
          isAnonymous: true
        };
        
        localStorage.setItem('phonepe_transaction_data', JSON.stringify(transactionData));

        // Load PhonePe SDK and initiate payment
        this.loadPhonePeSDK().then(() => {
          this.hideProgressBar();
          this.openPhonePeCheckout(tokenResponse.data.tokenUrl);
        }).catch((error) => {
          this.logger.error('Failed to load PhonePe SDK:', error);
          this.showPaymentFailedMessage();
          this.hideProgressBar();
        });
      },
      (error) => {
        this.logger.error('Payment initiation failed:', error);
        this.showPaymentFailedMessage();
        this.hideProgressBar();
      }
    );
  }

  /**
   * Verify anonymous payment
   */
  verifyPayment(request: AnonymousPaymentVerifyRequest): Observable<AnonymousPaymentVerifyResponse> {
    this.logger.debug('Verifying anonymous payment:', request);
    const url = `${this.API_BASE}/verify`;
    return this.http.post<AnonymousPaymentVerifyResponse>(url, request, this.getHttpOptions());
  }

  /**
   * Get anonymous payment receipt
   */
  getPaymentReceipt(bookingId: string): Observable<AnonymousReceiptResponse> {
    this.logger.debug('Getting anonymous payment receipt for booking:', bookingId);
    const url = `${this.API_BASE}/receipt/${bookingId}`;
    return this.http.get<AnonymousReceiptResponse>(url, this.getHttpOptions());
  }

  /**
   * Handle payment verification from callback
   */
  verifyPaymentFromCallback(merchantTransactionId: string, orderId: string, bookingId: string): void {
    const verifyRequest: AnonymousPaymentVerifyRequest = {
      merchantTransactionId,
      orderId,
      bookingId
    };

    this.verifyPayment(verifyRequest).subscribe(
      (response) => {
        this.logger.debug('Anonymous payment verification response:', response);
        
        if (!response.succeeded) {
          this.logger.error('Anonymous payment verification failed:', response.message);
          this.showPaymentFailedMessage();
          return;
        }

        const paymentStatus = response.data.paymentStatus;
        
        if (paymentStatus === 'COMPLETED') {
          this.paymentCompletedSubject.next(true);
          this.showPaymentSuccessMessage();
          this.router.navigate(['/receipt', bookingId]);
        } else {
          this.showPaymentFailedMessage();
          this.router.navigate(['/pay', bookingId]);
        }
      },
      (error) => {
        this.logger.error('Anonymous payment verification error:', error);
        this.showPaymentFailedMessage();
        this.router.navigate(['/pay', bookingId]);
      }
    );
  }

  // Utility methods
  private loadPhonePeSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.phonepeScriptLoaded) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://mercury.phonepe.com/web/checkout/sdk/v1/phonepe.js';
      script.onload = () => {
        window.phonepeScriptLoaded = true;
        resolve();
      };
      script.onerror = () => reject(new Error('Failed to load PhonePe SDK'));
      document.head.appendChild(script);
    });
  }

  private openPhonePeCheckout(tokenUrl: string): void {
    if (window.PhonePeCheckout) {
      window.PhonePeCheckout.transact({
        tokenUrl: tokenUrl,
        callback: (response: string) => {
          this.logger.debug('PhonePe callback response:', response);
          // Callback handling is done via URL redirect
        },
        type: 'page'
      });
    } else {
      this.logger.error('PhonePe SDK not loaded');
      this.showPaymentFailedMessage();
    }
  }

  private showProgressBar(): void {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar(): void {
    this.progressService.isPorgress.next(false);
  }

  private showPaymentSuccessMessage(): void {
    this.toastrService.success('Payment completed successfully!');
  }

  private showPaymentFailedMessage(): void {
    this.toastrService.error('Payment failed. Please try again.');
  }

  // Storage utilities for transaction data
  getStoredTransactionData(): any {
    const data = localStorage.getItem('phonepe_transaction_data');
    return data ? JSON.parse(data) : null;
  }

  clearStoredTransactionData(): void {
    localStorage.removeItem('phonepe_transaction_data');
  }
}
