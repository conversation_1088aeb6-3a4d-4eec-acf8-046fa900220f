import { Component, OnInit } from '@angular/core';
import { BookingAPIService } from 'src/app/shared/services/booking-api.service';
import { UserBooking, UserBookingsResponse } from 'src/app/shared/models/user-bookings.model';
import { LoggerService } from 'src/app/shared/interceptors/logger.service';
import { Router } from '@angular/router';
import { PaymentStatusUtil } from 'src/app/shared/utils/payment-status.util';

@Component({
  selector: 'app-user-rides',
  templateUrl: './user-rides.component.html',
  styleUrls: ['./user-rides.component.css']
})
export class UserRidesComponent implements OnInit {

  bookings: UserBooking[] = [];
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';

  // Pagination properties
  currentPageSize: number = 10;
  nextCursor: string | null = null;
  hasMoreResults: boolean = false;
  totalCount: number = 0;

  // UI state
  isLoadingMore: boolean = false;

  private logger: LoggerService;

  constructor(
    private bookingApiService: BookingAPIService,
    private router: Router
  ) {
    this.logger = LoggerService.createLogger('UserRidesComponent');
  }

  ngOnInit(): void {
    this.loadUserBookings();
  }

  loadUserBookings(loadMore: boolean = false): void {
    this.logger.trace('loadUserBookings() called with loadMore:', loadMore);

    if (loadMore) {
      this.isLoadingMore = true;
    } else {
      this.isLoading = true;
      this.bookings = [];
      this.nextCursor = null;
      this.totalCount = 0; // Reset total count when starting fresh
    }

    this.hasError = false;
    this.errorMessage = '';

    const request = {
      pageSize: this.currentPageSize,
      cursor: loadMore ? this.nextCursor : undefined
    };

    this.bookingApiService.getUserBookings(request).subscribe({
      next: (response: UserBookingsResponse) => {
        this.logger.trace('getUserBookings response:', response);

        if (response.succeeded && response.data) {
          if (loadMore) {
            this.bookings = [...this.bookings, ...response.data.bookings];
          } else {
            this.bookings = response.data.bookings;
          }

          this.nextCursor = response.data.nextCursor;
          this.hasMoreResults = response.data.hasMoreResult;

          // The totalCount should represent the actual total number of bookings loaded so far
          // Since the API doesn't provide a global total count, we use the current bookings length
          this.totalCount = this.bookings.length;
        } else {
          this.hasError = true;
          this.errorMessage = response.message || 'Failed to load bookings';
        }
      },
      error: (error) => {
        this.logger.error('Error loading user bookings:', error);
        this.hasError = true;
        this.errorMessage = 'Failed to load bookings. Please try again.';
      },
      complete: () => {
        this.isLoading = false;
        this.isLoadingMore = false;
      }
    });
  }

  loadMoreBookings(): void {
    if (this.hasMoreResults && !this.isLoadingMore) {
      this.loadUserBookings(true);
    }
  }

  refreshBookings(): void {
    this.loadUserBookings(false);
  }

  viewBookingDetails(bookingId: string): void {
    this.logger.trace('viewBookingDetails() called with bookingId:', bookingId);
    this.router.navigate(['/userprofile/booking-receipt', bookingId]);
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });
    } catch (error) {
      this.logger.error('Error formatting date:', error);
      return dateString;
    }
  }

  formatTime(timeString: string): string {
    if (!timeString) return '';

    try {
      // Handle time format like "13:00"
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));

      return date.toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      this.logger.error('Error formatting time:', error);
      return timeString;
    }
  }

  getStatusClass(status: string, booking?: UserBooking): string {
    return PaymentStatusUtil.getStatusClass(status, booking);
  }

  getStatusText(status: string, booking?: UserBooking): string {
    return PaymentStatusUtil.getDisplayText(status, booking);
  }

  trackByBookingId(index: number, booking: UserBooking): string {
    return booking.bookingId;
  }
}
