import { TestBed } from '@angular/core/testing';
import { BookingStateService, BookingFormState } from './booking-state.service';

describe('BookingStateService', () => {
  let service: BookingStateService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(BookingStateService);
    
    // Clear session storage before each test
    sessionStorage.clear();
  });

  afterEach(() => {
    // Clean up after each test
    sessionStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should store and retrieve booking state', () => {
    const mockState: BookingFormState = {
      pickUpCity: 'Kadapa',
      dropOffCity: 'Jaipur',
      pickUpAddress: 'State Bank of India COURT COMPLEX KADAPA',
      dropOffAddress: 'Jaipur railway station',
      pickUpAddressLatitutde: 14.4803864,
      pickUpAddressLongituted: 78.8340793,
      dropOffAddressLatitutde: 26.918938,
      dropOffAddressLongituted: 75.7887458,
      bookingDate: '2025-07-24',
      bookingTime: '04:00',
      selectedCarCategory: {
        categoryName: 'SUV Premium',
        categoryId: 'suv-premium',
        carFeatures: 'AC, GPS',
        carCapacity: 7,
        carImage: 'suv.png',
        basicFare: 5000,
        gst: 900,
        fare: 5900,
        duration: '12 hours',
        distance: 850,
        perKMCharges: '7',
        fixRateNote: 'Fixed rate',
        tollCharge: 500
      },
      tripType: 'One Way',
      timestamp: Date.now()
    };

    // Store the state
    service.storeBookingState(mockState);

    // Retrieve the state
    const retrievedState = service.getBookingState();

    expect(retrievedState).toBeTruthy();
    expect(retrievedState?.pickUpCity).toBe('Kadapa');
    expect(retrievedState?.dropOffCity).toBe('Jaipur');
    expect(retrievedState?.selectedCarCategory.categoryName).toBe('SUV Premium');
  });

  it('should return null when no booking state exists', () => {
    const state = service.getBookingState();
    expect(state).toBeNull();
  });

  it('should clear booking state', () => {
    const mockState: BookingFormState = {
      pickUpCity: 'Test City',
      dropOffCity: 'Test Destination',
      pickUpAddress: 'Test Address',
      dropOffAddress: 'Test Drop Address',
      pickUpAddressLatitutde: 0,
      pickUpAddressLongituted: 0,
      dropOffAddressLatitutde: 0,
      dropOffAddressLongituted: 0,
      bookingDate: '2025-07-24',
      bookingTime: '10:00',
      selectedCarCategory: {
        categoryName: 'Test Category',
        categoryId: 'test',
        carFeatures: 'Test Features',
        carCapacity: 4,
        carImage: 'test.png',
        basicFare: 1000,
        gst: 180,
        fare: 1180,
        duration: '2 hours',
        distance: 50,
        perKMCharges: '20',
        fixRateNote: 'Test note',
        tollCharge: 100
      },
      tripType: 'One Way',
      timestamp: Date.now()
    };

    service.storeBookingState(mockState);
    expect(service.hasBookingState()).toBe(true);

    service.clearBookingState();
    expect(service.hasBookingState()).toBe(false);
  });

  it('should store and retrieve booking query params', () => {
    const mockParams = {
      pickUpAddressLongLat: '14.4803864,78.8340793',
      dropOffAddressLongLat: '26.918938,75.7887458',
      tripType: 'One Way',
      categoryName: 'SUV Premium',
      from: 'Kadapa',
      to: 'Jaipur',
      pickupcity: 'Kadapa',
      dropoffcity: 'Jaipur',
      pickupdate: '2025-07-24',
      pickuptime: '04:00'
    };

    service.storeBookingQueryParams(mockParams);
    const retrievedParams = service.getBookingQueryParams();

    expect(retrievedParams).toBeTruthy();
    expect(retrievedParams.pickUpAddressLongLat).toBe('14.4803864,78.8340793');
    expect(retrievedParams.categoryName).toBe('SUV Premium');
  });

  it('should handle expired booking state', () => {
    // Create a mock state with an old timestamp (3 hours ago)
    const expiredTimestamp = Date.now() - (3 * 60 * 60 * 1000);
    const expiredState = {
      pickUpCity: 'Test',
      dropOffCity: 'Test',
      pickUpAddress: 'Test',
      dropOffAddress: 'Test',
      pickUpAddressLatitutde: 0,
      pickUpAddressLongituted: 0,
      dropOffAddressLatitutde: 0,
      dropOffAddressLongituted: 0,
      bookingDate: '2025-07-24',
      bookingTime: '10:00',
      selectedCarCategory: {
        categoryName: 'Test',
        categoryId: 'test',
        carFeatures: 'Test',
        carCapacity: 4,
        carImage: 'test.png',
        basicFare: 1000,
        gst: 180,
        fare: 1180,
        duration: '2 hours',
        distance: 50,
        perKMCharges: '20',
        fixRateNote: 'Test',
        tollCharge: 100
      },
      tripType: 'One Way',
      timestamp: expiredTimestamp
    };

    // Manually store expired state
    sessionStorage.setItem('cabyaari_booking_form_state', JSON.stringify(expiredState));

    // Try to retrieve - should return null and clear the expired data
    const retrievedState = service.getBookingState();
    expect(retrievedState).toBeNull();
    expect(sessionStorage.getItem('cabyaari_booking_form_state')).toBeNull();
  });
});
