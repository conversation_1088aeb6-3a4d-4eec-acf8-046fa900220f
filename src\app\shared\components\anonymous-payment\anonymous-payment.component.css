/* Anonymous Payment Component Styles */

.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.error-message h3 {
  color: #333;
  margin: 1rem 0;
}

.loading-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container .text-center {
  padding: 2rem;
}

.loading-container .spinner-border {
  width: 3rem;
  height: 3rem;
  color: #007bff;
}

/* Payment option styles */
.payment-option {
  margin: 1rem 0;
}

.payment-box {
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.payment-box:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.payment-box.selected {
  border-color: #007bff;
  background-color: #f8f9ff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.payment-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.payment-check {
  margin-right: 1rem;
}

.payment-rate {
  flex: 1;
}

.token-amount {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.driver-pay {
  display: block;
  font-size: 0.9rem;
  color: #666;
}

/* Custom checkbox styles */
.custom-checkbox {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: relative;
  display: inline-block;
  height: 20px;
  width: 20px;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-checkbox:hover input ~ .checkmark {
  border-color: #007bff;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #007bff;
  border-color: #007bff;
}

.custom-checkbox input:disabled ~ .checkmark {
  background-color: #007bff;
  border-color: #007bff;
  opacity: 0.8;
  cursor: not-allowed;
}

.custom-checkbox input:disabled {
  cursor: not-allowed;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Fare table styles */
.fare-table {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.fare-heading,
.amount-payable,
.driver-payable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.fare-heading:last-child,
.amount-payable:last-child,
.driver-payable:last-child {
  border-bottom: none;
}

.fare-heading {
  font-weight: 600;
  font-size: 1.1rem;
}

.amount-payable {
  font-weight: 600;
  color: #007bff;
}

.t-right {
  text-align: right;
  font-weight: 600;
}

/* Payment breakdown styles */
.payment-breakdown {
  border-top: 1px solid #e9ecef;
  padding-top: 1rem;
  margin-top: 1rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  font-size: 0.95rem;
  color: #666;
}

.breakdown-item:first-child {
  color: #007bff;
  font-weight: 600;
}

.breakdown-item:last-child {
  color: #28a745;
  font-weight: 600;
}



/* Pay now button */
.book-now {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  width: 100%;
  margin: 1rem 0;
}

.book-now:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.book-now:active {
  transform: translateY(0);
}

/* Booking details section */
.booking-heading {
  color: #333;
  margin-bottom: 2rem;
  text-align: center;
}

.booking-review > span {
  display: block;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 2rem;
  text-align: center;
}

.booking-invoice {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.booking-fare-table {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.trip-detail {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.trip-detail span {
  display: block;
  color: #666;
  margin-bottom: 0.5rem;
}

.big-heading {
  font-size: 2rem;
  font-weight: 700;
  color: #007bff;
}

.fare-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.fare-row:last-child {
  border-bottom: none;
}

.left-col {
  flex: 1;
}

.right-col {
  font-weight: 600;
  color: #333;
}

.b-head {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.total-fare {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-top: 1rem;
  border-top: 2px solid #007bff;
  font-size: 1.2rem;
  font-weight: 700;
  color: #007bff;
}

.terms-condition {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  margin-top: 2rem;
}

.terms-condition h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.terms-condition p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .payment-label {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .payment-check {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .booking-invoice,
  .booking-fare-table {
    margin-bottom: 1rem;
  }
  
  .fare-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .right-col {
    margin-top: 0.25rem;
  }
}
