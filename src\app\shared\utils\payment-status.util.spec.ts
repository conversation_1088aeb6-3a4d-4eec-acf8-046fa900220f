import { PaymentStatusUtil, PaymentStatusType } from './payment-status.util';

describe('PaymentStatusUtil', () => {
  
  describe('isPartialPayment', () => {
    it('should return true for partial payment booking', () => {
      const booking = { isPartialPayment: true, paidAmount: 1000 };
      expect(PaymentStatusUtil.isPartialPayment(booking)).toBe(true);
    });

    it('should return false for full payment booking', () => {
      const booking = { isPartialPayment: false, paidAmount: 4200 };
      expect(PaymentStatusUtil.isPartialPayment(booking)).toBe(false);
    });

    it('should return false for booking without isPartialPayment field', () => {
      const booking = { paidAmount: 4200 };
      expect(PaymentStatusUtil.isPartialPayment(booking)).toBe(false);
    });

    it('should return false for null booking', () => {
      expect(PaymentStatusUtil.isPartialPayment(null)).toBe(false);
    });
  });

  describe('getDisplayText', () => {
    it('should return "Partial Paid" for partial payment booking', () => {
      const booking = { isPartialPayment: true, paidAmount: 1000 };
      const result = PaymentStatusUtil.getDisplayText('Paid', booking);
      expect(result).toBe('Partial Paid');
    });

    it('should return "Paid" for full payment booking', () => {
      const booking = { isPartialPayment: false, paidAmount: 4200 };
      const result = PaymentStatusUtil.getDisplayText('Paid', booking);
      expect(result).toBe('Paid');
    });

    it('should return "Paid" for paid status without booking', () => {
      const result = PaymentStatusUtil.getDisplayText('Paid');
      expect(result).toBe('Paid');
    });

    it('should return "Pending" for pending status', () => {
      const result = PaymentStatusUtil.getDisplayText('Pending');
      expect(result).toBe('Pending');
    });

    it('should return "Failed" for failed status', () => {
      const result = PaymentStatusUtil.getDisplayText('Failed');
      expect(result).toBe('Failed');
    });
  });

  describe('getStatusClass', () => {
    it('should return "status-partial" for partial payment booking', () => {
      const booking = { isPartialPayment: true, paidAmount: 1000 };
      const result = PaymentStatusUtil.getStatusClass('Paid', booking);
      expect(result).toBe('status-partial');
    });

    it('should return "status-paid" for full payment booking', () => {
      const booking = { isPartialPayment: false, paidAmount: 4200 };
      const result = PaymentStatusUtil.getStatusClass('Paid', booking);
      expect(result).toBe('status-paid');
    });

    it('should return "status-paid" for paid status without booking', () => {
      const result = PaymentStatusUtil.getStatusClass('Paid');
      expect(result).toBe('status-paid');
    });

    it('should return "status-pending" for pending status', () => {
      const result = PaymentStatusUtil.getStatusClass('Pending');
      expect(result).toBe('status-pending');
    });

    it('should return "status-failed" for failed status', () => {
      const result = PaymentStatusUtil.getStatusClass('Failed');
      expect(result).toBe('status-failed');
    });
  });

  describe('normalizePaymentStatus', () => {
    it('should normalize "paid" to COMPLETED', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus('paid');
      expect(result).toBe(PaymentStatusType.COMPLETED);
    });

    it('should normalize "Paid" to COMPLETED', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus('Paid');
      expect(result).toBe(PaymentStatusType.COMPLETED);
    });

    it('should normalize "pending" to PENDING', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus('pending');
      expect(result).toBe(PaymentStatusType.PENDING);
    });

    it('should normalize "failed" to FAILED', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus('failed');
      expect(result).toBe(PaymentStatusType.FAILED);
    });

    it('should return UNKNOWN for unrecognized status', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus('unknown_status');
      expect(result).toBe(PaymentStatusType.UNKNOWN);
    });

    it('should return UNKNOWN for null status', () => {
      const result = PaymentStatusUtil.normalizePaymentStatus(null);
      expect(result).toBe(PaymentStatusType.UNKNOWN);
    });
  });
});
