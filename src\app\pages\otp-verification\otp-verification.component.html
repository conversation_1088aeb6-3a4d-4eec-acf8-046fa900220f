<app-progress-loader></app-progress-loader>

<app-sidemenu></app-sidemenu>

<header id="header" class="login-header">
    <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
    <div class="topheader loginheader"><a routerLink="/home" class="logo"><img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;" /></a>

    </div>
    <div class="loginform">
        <div class="col-12 col-12-small">
            <form method="post" action="#">
                <div class="row gtr-50">
                    <div class="col-12">
                        <h3>Verify OTP</h3>
                        <p>We have sent an OTP to {{phoneNumber}}</p>

                        <div class="inputrow">
                            <input type="text" name="otp" [(ngModel)]="otp"
                                class="form-control" placeholder="Enter OTP"
                                maxlength="6" required autofocus />
                        </div>
                        <div class="checkbox mb-3 text-danger" *ngIf="loginError">
                            OTP verification failed. Please try again.
                        </div>
                        <div class="login" (click)="verifyOtp()">Verify OTP</div>

                        <div class="bottombtn">
                            <a (click)="resendOtp()" [class.disabled]="isResending">
                                {{isResending ? 'Sending...' : 'Resend OTP'}}
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</header>

<div id="main" class="loginpage d-none d-lg-block col-lg-12">
    <section id="two" class="logincontent"></section>
</div>
