import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { AnonymousPaymentComponent } from './anonymous-payment.component';
import { AnonymousPaymentService } from '../../services/anonymous-payment.service';
import { ProgressService } from '../../services/progress.service';

describe('AnonymousPaymentComponent - URL Parameter Handling', () => {
  let component: AnonymousPaymentComponent;
  let fixture: ComponentFixture<AnonymousPaymentComponent>;
  let mockActivatedRoute: any;
  let mockRouter: any;
  let mockAnonymousPaymentService: any;
  let mockToastrService: any;
  let mockProgressService: any;

  beforeEach(async () => {
    // Mock services
    mockActivatedRoute = {
      params: of({}),
      snapshot: {
        queryParams: {}
      }
    };
    
    mockRouter = {
      navigate: jasmine.createSpy('navigate')
    };
    
    mockAnonymousPaymentService = {
      getBookingDetails: jasmine.createSpy('getBookingDetails').and.returnValue(
        of({
          succeeded: true,
          data: {
            bookingId: 'test-booking-id',
            totalAmount: 1000,
            onlinePaymentAmount: 500
          }
        })
      )
    };
    
    mockToastrService = {
      error: jasmine.createSpy('error')
    };
    
    mockProgressService = {
      isPorgress: {
        next: jasmine.createSpy('next')
      }
    };

    await TestBed.configureTestingModule({
      declarations: [AnonymousPaymentComponent],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: AnonymousPaymentService, useValue: mockAnonymousPaymentService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: ProgressService, useValue: mockProgressService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AnonymousPaymentComponent);
    component = fixture.componentInstance;
  });

  it('should extract booking ID from path parameter (/pay/:id)', () => {
    const testBookingId = 'VnHPu4YbEgV9OMB_ptwXSA~~';
    mockActivatedRoute.params = of({ id: testBookingId });
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(testBookingId);
    expect(mockAnonymousPaymentService.getBookingDetails).toHaveBeenCalledWith(testBookingId);
  });

  it('should extract booking ID from query parameter (/pay?id=xxx)', () => {
    const testBookingId = 'VnHPu4YbEgV9OMB_ptwXSA~~';
    mockActivatedRoute.params = of({}); // No path parameter
    mockActivatedRoute.snapshot.queryParams = { id: testBookingId };
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(testBookingId);
    expect(mockAnonymousPaymentService.getBookingDetails).toHaveBeenCalledWith(testBookingId);
  });

  it('should prioritize path parameter over query parameter', () => {
    const pathBookingId = 'path-booking-id';
    const queryBookingId = 'query-booking-id';
    
    mockActivatedRoute.params = of({ id: pathBookingId });
    mockActivatedRoute.snapshot.queryParams = { id: queryBookingId };
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(pathBookingId);
    expect(mockAnonymousPaymentService.getBookingDetails).toHaveBeenCalledWith(pathBookingId);
  });

  it('should handle error when no booking ID is provided', () => {
    mockActivatedRoute.params = of({}); // No path parameter
    mockActivatedRoute.snapshot.queryParams = {}; // No query parameter
    
    component.ngOnInit();
    
    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('Invalid booking ID');
    expect(mockToastrService.error).toHaveBeenCalledWith('Invalid booking ID');
  });
});
