<app-progress-loader></app-progress-loader>

<app-sidemenu></app-sidemenu>

<header id="header" class="registration-header">
    <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
    <div class="topheader loginheader">
        <a routerLink="/home" class="logo">
            <img src="{{imageFolderPath}}/logo.png"alt="" style="height: 48px; width: 175px;" />
        </a>
    </div>
    
    <div class="loginform">
        <div class="col-12 col-12-small">
            <form method="post" action="#">
                <div class="row gtr-50">
                    <div class="col-12">
                        <h3>{{REGISTERATION_HEADER}}</h3>
                        
                        <div class="inputrow">
                            <input type="text" name="firstName" [(ngModel)]="userRegistration.firstName" 
                                class="form-control" placeholder="First Name" required />
                        </div>

                        <div class="inputrow">
                            <input type="text" name="lastName" [(ngModel)]="userRegistration.lastName" 
                                class="form-control" placeholder="Last Name" required />
                        </div>

                        <div class="inputrow">
                            <input type="text" name="phoneNumber" [(ngModel)]="userRegistration.phoneNumber"
                                class="form-control" placeholder="Phone Number" required />
                        </div>

                        <div class="login" (click)="validateAndSubmit()">Sign Up</div>
                    </div>
                </div>
            </form>

        </div>
    </div>
</header>
