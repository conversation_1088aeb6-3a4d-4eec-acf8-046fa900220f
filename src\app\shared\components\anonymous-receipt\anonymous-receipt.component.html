<!-- Error State -->
<div *ngIf="hasError" class="error-container">
    <div class="error-content">
        <div class="topheader">
            <a routerLink="/home" class="logo">
                <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
            </a>
        </div>
        <div class="error-message">
            <i class="fa fa-exclamation-triangle" style="color: #ff6b6b; font-size: 3em; margin-bottom: 1rem;"></i>
            <h2>{{ errorMessage }}</h2>
            <button class="btn btn-primary mt-3" (click)="navigateHome()">Go to Home</button>
        </div>
    </div>
</div>

<!-- Loading State -->
<div *ngIf="isLoading && !hasError" class="loading-container">
    <div class="topheader">
        <a routerLink="/home" class="logo">
            <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
        </a>
    </div>
    <div class="loading-content">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
        <p class="mt-3">Loading your receipt...</p>
    </div>
</div>

<!-- Main Receipt Content -->
<div *ngIf="!isLoading && !hasError && receiptData" id="main" class="receipt-container main-override">
    
    <!-- Header -->
    <div class="receipt-header">
        <div class="topheader">
            <a routerLink="/home" class="logo">
                <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
            </a>
        </div>
        
        <div class="receipt-status">
            <i class="fa" [ngClass]="getPaymentStatusIcon()" [style.color]="getPaymentStatusColor()"></i>
            <h2>{{ getPaymentStatusText() }}</h2>
            <h3>
                <span class="booking-id">Booking ID: <strong>{{ bookingId }}</strong></span>
                <br>
                <span class="receipt-note" *ngIf="isPaymentCompleted()">
                    Thank you for choosing CabYaari! Your booking has been confirmed.
                </span>
                <span class="receipt-note" *ngIf="isPaymentPending()">
                    Your booking is confirmed. Payment is pending.
                </span>
            </h3>
        </div>

        <!-- Action Buttons -->
        <div class="receipt-actions">
            <button class="btn btn-outline-primary" (click)="shareReceipt()">
                <i class="fa fa-share-alt"></i> Share Receipt
            </button>
            <button class="btn btn-outline-secondary" (click)="downloadReceipt()">
                <i class="fa fa-download"></i> Download
            </button>
            <button class="btn btn-primary" (click)="navigateHome()">
                <i class="fa fa-home"></i> Go to Home
            </button>
        </div>
    </div>

    <!-- Receipt Details -->
    <div class="receipt-content">
        <div class="row">
            <!-- Trip Details -->
            <div class="col-12 col-md-6">
                <div class="receipt-section">
                    <h4>Trip Details</h4>
                    <div class="trip-route">
                        <div class="route-point">
                            <div class="route-icon pickup">
                                <i class="fa fa-circle"></i>
                            </div>
                            <div class="route-details">
                                <div class="city">{{ receiptData.pickUpCity }}</div>
                                <div class="date-time">
                                    <span class="date">{{ formatDate(receiptData.pickUpDate) }}</span>
                                    <span class="time">{{ getFormattedPickupTime() }}</span>
                                </div>
                                <div class="address">{{ receiptData.pickUpAddress }}</div>
                            </div>
                        </div>

                        <div class="route-line"></div>

                        <div class="route-point">
                            <div class="route-icon dropoff">
                                <i class="fa fa-map-marker-alt"></i>
                            </div>
                            <div class="route-details">
                                <div class="city">{{ receiptData.dropOffCity }}</div>
                                <div class="date-time">
                                    <span class="date">{{ calculateDropOffDateTime().date }}</span>
                                    <span class="time">{{ calculateDropOffDateTime().time }}</span>
                                </div>
                                <div class="address">{{ receiptData.dropOffAddress }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="trip-info">
                        <div class="info-row">
                            <span class="label">Trip Type:</span>
                            <span class="value">{{ receiptData.tripType }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Distance:</span>
                            <span class="value">{{ getDisplayDistance() }} KM</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Duration:</span>
                            <span class="value">{{ receiptData.duration }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Car Category:</span>
                            <span class="value">{{ receiptData.carCategory }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Capacity:</span>
                            <span class="value">{{ receiptData.carCapacity }} passengers</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Pickup Date & Time:</span>
                            <span class="value">{{ formatDate(receiptData.pickUpDate) }} at {{ getFormattedPickupTime() }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Drop-off Date & Time:</span>
                            <span class="value">{{ calculateDropOffDateTime().date }} at {{ calculateDropOffDateTime().time }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="col-12 col-md-6">
                <div class="receipt-section">
                    <h4>Payment Summary</h4>
                    <div class="payment-details">
                        <div class="fare-breakdown">
                            <div class="fare-row">
                                <span class="label">Base Fare:</span>
                                <span class="value">₹ {{ receiptData.basicFare }}</span>
                            </div>
                            <div class="fare-row">
                                <span class="label">Taxes & Fees:</span>
                                <span class="value">₹ {{ receiptData.gst }}</span>
                            </div>
                            <div class="fare-row total-row">
                                <span class="label">Total Fare:</span>
                                <span class="value">₹ {{ receiptData.totalFare }}</span>
                            </div>
                        </div>

                        <div class="payment-info">
                            <div class="info-row">
                                <span class="label">Payment Status:</span>
                                <span class="value" [style.color]="getPaymentStatusColor()">
                                    {{ receiptData.paymentStatus }}
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="label">Amount Paid:</span>
                                <span class="value success">₹ {{ receiptData.amountPaid }}</span>
                            </div>
                            <div class="info-row" *ngIf="receiptData.remainingAmountForDriver > 0">
                                <span class="label">Cash to Driver:</span>
                                <span class="value warning">₹ {{ receiptData.remainingAmountForDriver }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">Payment Method:</span>
                                <span class="value">{{ receiptData.paymentType }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">Transaction ID:</span>
                                <span class="value">{{ receiptData.transactionId }}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">Payment Date:</span>
                                <span class="value">{{ formatDateTime(receiptData.paymentDate) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Traveler Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="receipt-section">
                    <h4>Traveler Information</h4>
                    <div class="traveler-info">
                        <div class="info-row">
                            <span class="label">Name:</span>
                            <span class="value">{{ receiptData.travelerName }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Phone:</span>
                            <span class="value">{{ receiptData.phoneNumber }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">Email:</span>
                            <span class="value">{{ receiptData.mailId }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Notes -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="receipt-section important-notes">
                    <h4>Important Information</h4>
                    <ul>
                        <li *ngIf="receiptData.remainingAmountForDriver > 0">
                            Please pay ₹{{ receiptData.remainingAmountForDriver }} in cash to the driver at the start of your journey.
                        </li>
                        <li>Driver details will be shared via SMS and email 30 minutes before pickup time.</li>
                        <li>For any assistance, please contact our customer support.</li>
                        <li>Parking and toll charges (if any) are to be paid directly to authorities.</li>
                        <li>Waiting charges of ₹200/hour apply after 30 minutes of scheduled pickup time.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
