import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { AnonymousReceiptComponent } from './anonymous-receipt.component';
import { AnonymousPaymentService } from '../../services/anonymous-payment.service';
import { ProgressService } from '../../services/progress.service';

describe('AnonymousReceiptComponent - URL Parameter Handling', () => {
  let component: AnonymousReceiptComponent;
  let fixture: ComponentFixture<AnonymousReceiptComponent>;
  let mockActivatedRoute: any;
  let mockRouter: any;
  let mockAnonymousPaymentService: any;
  let mockToastrService: any;
  let mockProgressService: any;

  beforeEach(async () => {
    // Mock services
    mockActivatedRoute = {
      params: of({}),
      snapshot: {
        queryParams: {}
      }
    };
    
    mockRouter = {
      navigate: jasmine.createSpy('navigate')
    };
    
    mockAnonymousPaymentService = {
      getPaymentReceipt: jasmine.createSpy('getPaymentReceipt').and.returnValue(
        of({
          succeeded: true,
          data: {
            bookingId: 'test-booking-id',
            paymentStatus: 'COMPLETED',
            totalAmount: 1000
          }
        })
      )
    };
    
    mockToastrService = {
      error: jasmine.createSpy('error')
    };
    
    mockProgressService = {
      isPorgress: {
        next: jasmine.createSpy('next')
      }
    };

    await TestBed.configureTestingModule({
      declarations: [AnonymousReceiptComponent],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: Router, useValue: mockRouter },
        { provide: AnonymousPaymentService, useValue: mockAnonymousPaymentService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: ProgressService, useValue: mockProgressService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AnonymousReceiptComponent);
    component = fixture.componentInstance;
  });

  it('should extract booking ID from path parameter (/receipt/:id)', () => {
    const testBookingId = 'VnHPu4YbEgV9OMB_ptwXSA~~';
    mockActivatedRoute.params = of({ id: testBookingId });
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(testBookingId);
    expect(mockAnonymousPaymentService.getPaymentReceipt).toHaveBeenCalledWith(testBookingId);
  });

  it('should extract booking ID from query parameter (/receipt?id=xxx)', () => {
    const testBookingId = 'VnHPu4YbEgV9OMB_ptwXSA~~';
    mockActivatedRoute.params = of({}); // No path parameter
    mockActivatedRoute.snapshot.queryParams = { id: testBookingId };
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(testBookingId);
    expect(mockAnonymousPaymentService.getPaymentReceipt).toHaveBeenCalledWith(testBookingId);
  });

  it('should prioritize path parameter over query parameter', () => {
    const pathBookingId = 'path-booking-id';
    const queryBookingId = 'query-booking-id';
    
    mockActivatedRoute.params = of({ id: pathBookingId });
    mockActivatedRoute.snapshot.queryParams = { id: queryBookingId };
    
    component.ngOnInit();
    
    expect(component.bookingId).toBe(pathBookingId);
    expect(mockAnonymousPaymentService.getPaymentReceipt).toHaveBeenCalledWith(pathBookingId);
  });

  it('should handle error when no booking ID is provided', () => {
    mockActivatedRoute.params = of({}); // No path parameter
    mockActivatedRoute.snapshot.queryParams = {}; // No query parameter
    
    component.ngOnInit();
    
    expect(component.hasError).toBe(true);
    expect(component.errorMessage).toBe('Invalid booking ID');
    expect(mockToastrService.error).toHaveBeenCalledWith('Invalid booking ID');
  });
});
