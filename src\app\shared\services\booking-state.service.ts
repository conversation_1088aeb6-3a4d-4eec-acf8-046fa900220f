import { Injectable } from '@angular/core';
import { LoggerService } from '../interceptors/logger.service';

export interface BookingFormState {
  // Pickup/Dropoff Details
  pickUpCity: string;
  dropOffCity: string;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpAddressLatitutde: number;
  pickUpAddressLongituted: number;
  dropOffAddressLatitutde: number;
  dropOffAddressLongituted: number;
  
  // Date and Time
  bookingDate: string;
  bookingTime: string;
  
  // Car Category
  selectedCarCategory: {
    categoryName: string;
    categoryId: string;
    carFeatures: string;
    carCapacity: number;
    carImage: string;
    basicFare: number;
    gst: number;
    fare: number;
    duration: string;
    distance: number;
    perKMCharges: string;
    fixRateNote: string;
    tollCharge: number;
  };
  
  // Trip Type
  tripType: string;
  
  // Timestamp for expiry
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class BookingStateService {
  private readonly STORAGE_KEY = 'cabyaari_booking_form_state';
  private readonly EXPIRY_HOURS = 2; // Data expires after 2 hours
  private logger: LoggerService;

  constructor() {
    this.logger = LoggerService.createLogger('BookingStateService');
  }

  /**
   * Store booking form state in session storage
   */
  storeBookingState(state: BookingFormState): void {
    try {
      const stateWithTimestamp: BookingFormState = {
        ...state,
        timestamp: Date.now()
      };
      
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(stateWithTimestamp));
      this.logger.trace('Booking state stored successfully', stateWithTimestamp);
    } catch (error) {
      this.logger.error('Error storing booking state:', error);
    }
  }

  /**
   * Retrieve booking form state from session storage
   */
  getBookingState(): BookingFormState | null {
    try {
      const storedData = sessionStorage.getItem(this.STORAGE_KEY);
      if (!storedData) {
        this.logger.trace('No booking state found in session storage');
        return null;
      }

      const state: BookingFormState = JSON.parse(storedData);
      
      // Check if data has expired
      if (this.isStateExpired(state)) {
        this.logger.trace('Booking state has expired, removing from storage');
        this.clearBookingState();
        return null;
      }

      this.logger.trace('Booking state retrieved successfully', state);
      return state;
    } catch (error) {
      this.logger.error('Error retrieving booking state:', error);
      this.clearBookingState(); // Clear corrupted data
      return null;
    }
  }

  /**
   * Clear booking form state from session storage
   */
  clearBookingState(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY);
      this.logger.trace('Booking state cleared from session storage');
    } catch (error) {
      this.logger.error('Error clearing booking state:', error);
    }
  }

  /**
   * Check if booking state exists in session storage
   */
  hasBookingState(): boolean {
    const state = this.getBookingState();
    return state !== null;
  }

  /**
   * Update specific fields in the stored booking state
   */
  updateBookingState(updates: Partial<BookingFormState>): void {
    const currentState = this.getBookingState();
    if (currentState) {
      const updatedState: BookingFormState = {
        ...currentState,
        ...updates,
        timestamp: Date.now() // Update timestamp
      };
      this.storeBookingState(updatedState);
    }
  }

  /**
   * Check if the stored state has expired
   */
  private isStateExpired(state: BookingFormState): boolean {
    if (!state.timestamp) {
      return true; // No timestamp means old data, consider expired
    }
    
    const expiryTime = this.EXPIRY_HOURS * 60 * 60 * 1000; // Convert hours to milliseconds
    const currentTime = Date.now();
    
    return (currentTime - state.timestamp) > expiryTime;
  }

  /**
   * Store booking query parameters for back navigation
   */
  storeBookingQueryParams(queryParams: any): void {
    try {
      const paramsWithTimestamp = {
        ...queryParams,
        timestamp: Date.now()
      };
      
      sessionStorage.setItem('cabyaari_booking_query_params', JSON.stringify(paramsWithTimestamp));
      this.logger.trace('Booking query params stored successfully', paramsWithTimestamp);
    } catch (error) {
      this.logger.error('Error storing booking query params:', error);
    }
  }

  /**
   * Retrieve booking query parameters
   */
  getBookingQueryParams(): any | null {
    try {
      const storedData = sessionStorage.getItem('cabyaari_booking_query_params');
      if (!storedData) {
        return null;
      }

      const params = JSON.parse(storedData);
      
      // Check if data has expired (same expiry as booking state)
      if (params.timestamp && this.isParamsExpired(params)) {
        sessionStorage.removeItem('cabyaari_booking_query_params');
        return null;
      }

      return params;
    } catch (error) {
      this.logger.error('Error retrieving booking query params:', error);
      return null;
    }
  }

  /**
   * Clear booking query parameters
   */
  clearBookingQueryParams(): void {
    try {
      sessionStorage.removeItem('cabyaari_booking_query_params');
      this.logger.trace('Booking query params cleared from session storage');
    } catch (error) {
      this.logger.error('Error clearing booking query params:', error);
    }
  }

  private isParamsExpired(params: any): boolean {
    if (!params.timestamp) {
      return true;
    }
    
    const expiryTime = this.EXPIRY_HOURS * 60 * 60 * 1000;
    const currentTime = Date.now();
    
    return (currentTime - params.timestamp) > expiryTime;
  }
}
