/* Anonymous Payment Callback Component Styles */

.payment-callback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  flex-direction: column;
}

.callback-header {
  background: #fff;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.topheader {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo img {
  transition: transform 0.3s ease;
}

.logo:hover img {
  transform: scale(1.05);
}

.callback-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.status-container {
  background: #fff;
  border-radius: 16px;
  padding: 3rem 2rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.status-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.spinner-container {
  margin-bottom: 2rem;
}

.spinner-border {
  width: 4rem;
  height: 4rem;
  color: #007bff;
  border-width: 0.3em;
}

.status-icon {
  margin-bottom: 2rem;
  animation: fadeInScale 0.6s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.status-message h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
  animation: fadeInUp 0.8s ease-out;
}

.status-message p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.5;
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-info {
  margin: 2rem 0;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #28a745);
  border-radius: 3px;
  animation: progressFill 3s ease-in-out infinite;
}

@keyframes progressFill {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.progress-text {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
  margin: 0;
}

.action-buttons {
  margin-top: 2rem;
}

.action-buttons .btn {
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.action-buttons .btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.action-buttons .btn-primary:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.action-buttons .btn i {
  margin-right: 0.5rem;
}

.callback-footer {
  background: #fff;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  margin-top: auto;
}

.info-text {
  text-align: center;
}

.info-text p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

.info-text i {
  color: #007bff;
  margin-right: 0.5rem;
}

/* Success State Styles */
.status-container.success {
  border-top: 4px solid #28a745;
}

.status-container.success .status-message h2 {
  color: #28a745;
}

/* Error State Styles */
.status-container.error {
  border-top: 4px solid #dc3545;
}

.status-container.error .status-message h2 {
  color: #dc3545;
}

/* Warning State Styles */
.status-container.warning {
  border-top: 4px solid #ffc107;
}

.status-container.warning .status-message h2 {
  color: #856404;
}

/* Processing State Styles */
.status-container.processing {
  border-top: 4px solid #007bff;
}

.status-container.processing .status-message h2 {
  color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .callback-header {
    padding: 1rem;
  }
  
  .callback-content {
    padding: 1rem;
  }
  
  .status-container {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
  
  .status-message h2 {
    font-size: 1.5rem;
  }
  
  .status-message p {
    font-size: 1rem;
  }
  
  .action-buttons .btn {
    width: 100%;
    max-width: 250px;
  }
  
  .callback-footer {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .status-container {
    padding: 1.5rem 1rem;
  }
  
  .status-message h2 {
    font-size: 1.3rem;
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
  
  .status-icon i {
    font-size: 2.5rem !important;
  }
}

/* Animation for better UX */
.payment-callback-container {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* Loading state pulse effect */
.spinner-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border: 2px solid rgba(0,123,255,0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}
