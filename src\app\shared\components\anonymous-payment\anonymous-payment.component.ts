import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { AppConfig } from 'src/app/configs/app.config';
import { LoggerService } from '../../interceptors/logger.service';
import { ProgressService } from '../../services/progress.service';
import { AnonymousPaymentService } from '../../services/anonymous-payment.service';
import {
  AnonymousBookingDetails,
  AnonymousPaymentOption
} from '../../models/anonymous-payment.model';

@Component({
  selector: 'app-anonymous-payment',
  templateUrl: './anonymous-payment.component.html',
  styleUrls: ['./anonymous-payment.component.css']
})
export class AnonymousPaymentComponent implements OnInit, OnDestroy {
  private logger: LoggerService;
  private routeSubscription: Subscription;
  private bookingDetailsSubscription: Subscription;

  imageFolderPath: string = AppConfig.imageFolderPath;
  bookingId: string;
  bookingDetails: AnonymousBookingDetails;
  isLoading: boolean = true;
  hasError: boolean = false;
  errorMessage: string = '';

  // Payment options (both partial and full payment supported)
  selectedPaymentOption: AnonymousPaymentOption = AnonymousPaymentOption.FULL;
  amountToPay: number = 0;
  isPartialPaymentAvailable: boolean = false;



  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private anonymousPaymentService: AnonymousPaymentService
  ) {
    this.logger = LoggerService.createLogger('AnonymousPaymentComponent');
  }

  ngOnInit(): void {
    this.logger.trace('AnonymousPaymentComponent:: ngOnInit() called');
    this.routeSubscription = this.route.params.subscribe(params => {
      // First try to get ID from path parameter (/pay/:id)
      this.bookingId = params['id'];

      // If no path parameter, try query parameter (/pay/?id=xxx)
      if (!this.bookingId) {
        this.bookingId = this.route.snapshot.queryParams['id'];
      }

      if (this.bookingId) {
        this.loadBookingDetails();
      } else {
        this.handleError('Invalid booking ID');
      }
    });
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.bookingDetailsSubscription) {
      this.bookingDetailsSubscription.unsubscribe();
    }
  }

  private loadBookingDetails(): void {
    this.isLoading = true;
    this.showProgressBar();

    this.bookingDetailsSubscription = this.anonymousPaymentService.getBookingDetails(this.bookingId)
      .subscribe(
        (response) => {
          this.hideProgressBar();
          this.isLoading = false;

          if (response.succeeded && response.data) {
            this.bookingDetails = response.data;
            this.initializePaymentOptions();
          } else {
            this.handleError(response.message || 'Failed to load booking details');
          }
        },
        (error) => {
          this.hideProgressBar();
          this.isLoading = false;
          this.logger.error('Error loading booking details:', error);
          this.handleError('Unable to load booking details. Please check the booking ID and try again.');
        }
      );
  }

  private initializePaymentOptions(): void {
    // Check if partial payment is available based on API response
    this.isPartialPaymentAvailable = this.bookingDetails.isPartialPayment === true &&
                                     this.bookingDetails.partialPaymentAmount > 0;

    this.logger.debug('Partial payment availability:', {
      isPartialPayment: this.bookingDetails.isPartialPayment,
      partialPaymentAmount: this.bookingDetails.partialPaymentAmount,
      isPartialPaymentAvailable: this.isPartialPaymentAvailable
    });

    // Set payment option based on availability (no user choice)
    if (this.isPartialPaymentAvailable) {
      this.selectedPaymentOption = AnonymousPaymentOption.PARTIAL;
    } else {
      this.selectedPaymentOption = AnonymousPaymentOption.FULL;
    }

    this.updateAmountToPay();
  }

  private updateAmountToPay(): void {
    if (this.isPartialPaymentAvailable) {
      this.amountToPay = this.bookingDetails.partialPaymentAmount || this.bookingDetails.onlinePaymentAmount || 0;
    } else {
      this.amountToPay = this.bookingDetails.totalFare;
    }

    this.logger.debug('Amount to pay updated:', {
      selectedPaymentOption: this.selectedPaymentOption,
      isPartialPaymentAvailable: this.isPartialPaymentAvailable,
      amountToPay: this.amountToPay
    });
  }





  initiatePayment(): void {
    if (!this.bookingDetails || this.amountToPay <= 0) {
      this.toastrService.error('Invalid payment amount');
      return;
    }

    this.logger.debug('Initiating payment:', {
      bookingId: this.bookingId,
      paymentOption: this.selectedPaymentOption,
      amountToPay: this.amountToPay
    });

    this.anonymousPaymentService.startPhonePePayment(
      this.bookingId,
      this.selectedPaymentOption,
      this.amountToPay
    );
  }

  navigateHome(): void {
    this.router.navigate(['/home']);
  }

  private handleError(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    this.toastrService.error(message);
  }

  private showProgressBar(): void {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar(): void {
    this.progressService.isPorgress.next(false);
  }

  // Utility methods for template
  getCashAmountToPayDriver(): number {
    if (this.isPartialPaymentAvailable) {
      return this.bookingDetails.remainingAmountForDriver || this.bookingDetails.driverPaymentAmount || this.bookingDetails.cashAmountToPayDriver || 0;
    }
    return 0;
  }

  getPartialPaymentAmount(): number {
    return this.bookingDetails.partialPaymentAmount || this.bookingDetails.onlinePaymentAmount || 0;
  }

  getDisplayDistance(): number {
    // Prefer actual distance over billing distance
    return this.bookingDetails.actualDistance || this.bookingDetails.distance;
  }
}
