<app-progress-loader></app-progress-loader>

<!-- Header -->
<header id="header" class="smallheader" > 
    <span class="toggle"><a routerLink="/home" class="fa fa-arrow-left"></a> </span>

    <div class="topheader"><a routerLink="/home" class="logo"><img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;" /></a>
    
    </div> 
    <div class="forminner">
        <div class="head"><span></span>Enter Pickup and Dropoff Location</div>
        <form>
            <div class="form-group">

                <input #pickUpCity required type="email" placeholder="Enter Pickup Address" class="form-control" matInput [formControl]="pickUpCityAddressControl"
                [matAutocomplete]="pickUpAddressCity">
              <mat-autocomplete #pickUpAddressCity="matAutocomplete" (optionSelected)="_filterPickUpCityAddressLatLong($event.option.value)">
                <mat-option *ngFor="let pickupCitiesAddress of filteredOptionsPickUpCityAddress | async" [value]="pickupCitiesAddress">
                  {{pickupCitiesAddress}}
                </mat-option>
              </mat-autocomplete>
               
            </div>
          
              <div class="form-group">
                <input #dropOffCity required type="email" placeholder="Enter Dropoff Address" class="form-control" matInput [formControl]="dropOffCityAddressControl"
                [matAutocomplete]="dropOffAddressCity" >
              <mat-autocomplete #dropOffAddressCity="matAutocomplete" (optionSelected)="_filterDropOffCityAddressLatLong($event.option.value)">
                <mat-option *ngFor="let dropOffCitiesAddress of filteredOptionsDropOffCityAddress | async" [value]="dropOffCitiesAddress">
                  {{dropOffCitiesAddress}}
                </mat-option>
                
              </mat-autocomplete>
           
              </div>
              
              <div class="form-group custom-datepicker-wrapper">
                <label for="customDatepicker">
                  <input #pickupDt required type="text" id="customDatepicker" placeholder="dd-mm-yyyy"
                    autocomplete="off" class="form-control custom-datepicker" [formControl]="pickUpDateControl">
                  <div class="icon-calendar" (click)="openDatePicker()">
                    <img src="assets/images/calendar-svgrepo-com.svg" alt="Calendar">
                  </div>
                </label>
              </div>
              <div class="form-group custom-timepicker-wrapper">
                <label for="customTimepicker">
                  <input #pickTm type="text" class="time-pickable custom-timepicker" id="customTimepicker"
                    placeholder="Time of Journey" readonly [formControl]="pickUpTimeControl">
                  <div class="icon-timepicker" (click)="openTimePicker()">
                    <img src="assets/images/clock-ten-svgrepo-com.svg" alt="Clock">
                  </div>
                </label>
              </div>
        </form>
    </div>

    <div *ngIf="bookingRouteInfo" class="forminner" id="routeDetails">
      <div class="head">
        <span></span>
        Route Details
      </div>

      <div id="routeDiv">
        <div class="route-box">
          <label class="route-label">
            <div class="route-info">
              <span class="route-field">Duration</span>
              <span class="route-metric">{{bookingRouteInfo.durationString}}</span>
            </div>
          </label>
        </div>

        <div class="route-box">
          <label class="route-label">
            <div class="route-info">
              <span class="route-field">Distance</span>
              <span class="route-metric">{{bookingRouteInfo.distanceString}}</span>
            </div>
          </label>
        </div>
      </div>
    </div>

    <div *ngIf="carCategoryFareList" class="cabList">
      <ul>
          <li 
            *ngFor="let carCategory of carCategoryFareList; let i = index" 
            (click)="selectCategoryIndex(i)"
            [class.highlighted]="i === selectedCarCategoryIndex">
              <div class="cabType"><img src="{{imageFolderPath}}/{{carCategory.carCategoryList.categoryImage}}"></div>
              <div class="cabInfo">
                  <div class="modalType">
                      <span>{{carCategory.carCategoryList.categoryName}} </span><small> {{carCategory.carCategoryList.features}} </small>
                      <div class="price"><span>Fare:</span> Rs {{carCategory.fareDistanceCalculateList.fare}}</div>
                  </div>
                  <div class="bookingDetail">
                      <span><i class="fa fa-user"></i> &nbsp; {{carCategory.carCategoryList.capacity}} </span>
                      <span>
                        <a 
                          class="fa fa-info-circle" 
                          data-toggle="tooltip" 
                          data-placement="top" 
                          title="{{carCategory.carCategoryList.features}}">
                        </a>
                      </span>
                  </div>
              </div>
          </li>
      </ul>
  </div>

  <div class="bookBtn">
    <div class="book-now" (click)="validateAndSubmit()">Book Now</div>
    <div class="enquire-now" (click)="openWhatsApp()">Enquire Now</div>
  </div>
</header>

<!-- Main -->
<div id="main" class="mappage">
    <div id="map"></div>		
</div>
