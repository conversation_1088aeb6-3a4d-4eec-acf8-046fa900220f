/* Anonymous Receipt Component Styles */

.error-container,
.loading-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.error-content,
.loading-content {
  text-align: center;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  max-width: 500px;
  width: 90%;
}

.error-message h2 {
  color: #333;
  margin: 1rem 0;
}

.loading-content .spinner-border {
  width: 3rem;
  height: 3rem;
  color: #007bff;
}

.loading-content p {
  color: #666;
  margin-top: 1rem;
}

/* Receipt Container */
.receipt-container {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 2rem 0;
}

.receipt-header {
  background: #fff;
  padding: 2rem;
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0,0,0,0.1);
  text-align: center;
}

.receipt-status {
  margin: 2rem 0;
}

.receipt-status i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.receipt-status h2 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.booking-id {
  font-size: 1.2rem;
  color: #007bff;
}

.receipt-note {
  color: #666;
  font-size: 1rem;
  font-weight: normal;
}

.receipt-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.receipt-actions .btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.receipt-actions .btn i {
  margin-right: 0.5rem;
}

/* Receipt Content */
.receipt-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.receipt-section {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.receipt-section h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

/* Trip Route Styles */
.trip-route {
  margin-bottom: 2rem;
}

.route-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.route-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.route-icon.pickup {
  background: #28a745;
  color: white;
}

.route-icon.dropoff {
  background: #dc3545;
  color: white;
}

.route-details {
  flex: 1;
}

.route-details .city {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.route-details .date-time {
  margin-bottom: 0.5rem;
}

.route-details .date {
  display: block;
  font-weight: 500;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.route-details .time {
  display: block;
  font-weight: 600;
  color: #007bff;
  font-size: 1rem;
}

.route-details .address {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.route-line {
  width: 2px;
  height: 30px;
  background: #ddd;
  margin-left: 14px;
  margin-bottom: 1rem;
}

/* Info Rows */
.trip-info,
.payment-info,
.traveler-info {
  margin-top: 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row .label {
  color: #666;
  font-weight: 500;
}

.info-row .value {
  color: #333;
  font-weight: 600;
  text-align: right;
}

.info-row .value.success {
  color: #28a745;
}

.info-row .value.warning {
  color: #ffc107;
}

/* Payment Details */
.fare-breakdown {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.fare-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.fare-row:last-child {
  border-bottom: none;
}

.fare-row.total-row {
  border-top: 2px solid #007bff;
  margin-top: 0.5rem;
  padding-top: 1rem;
  font-size: 1.1rem;
  font-weight: 700;
  color: #007bff;
}

/* Important Notes */
.important-notes {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-left: 4px solid #ffc107;
}

.important-notes h4 {
  color: #856404;
  border-bottom-color: #ffc107;
}

.important-notes ul {
  margin: 0;
  padding-left: 1.5rem;
}

.important-notes li {
  color: #856404;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.important-notes li:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .receipt-container {
    padding: 1rem 0;
  }

  #main.main-override {
    margin: 0 !important;
  }
  
  .receipt-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .receipt-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .receipt-actions .btn {
    width: 100%;
    max-width: 250px;
  }
  
  .receipt-section {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .info-row .value {
    text-align: left;
  }
  
  .fare-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .route-point {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .route-icon {
    margin-bottom: 0.5rem;
  }
  
  .route-line {
    margin-left: 0;
    width: 100%;
    height: 2px;
  }
}

@media (max-width: 480px) {
  .receipt-header {
    padding: 1rem;
  }
  
  .receipt-section {
    padding: 1rem;
  }
  
  .receipt-status h2 {
    font-size: 1.5rem;
  }
  
  .booking-id {
    font-size: 1rem;
  }
  
  .receipt-actions .btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}

/* Print Styles */
@media print {
  .receipt-actions {
    display: none;
  }
  
  .receipt-container {
    background: white;
    padding: 0;
  }
  
  .receipt-section {
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .important-notes {
    background: #f8f9fa !important;
    -webkit-print-color-adjust: exact;
  }
}

.main-override {
  margin-left: 15% !important;
}