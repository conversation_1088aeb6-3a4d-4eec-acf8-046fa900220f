import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, of, from } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AppConfig } from 'src/app/configs/app.config';
import { City } from '../../models/city_model';
import { CarCategory } from '../../models/car-category.model';
import { ETA } from '../../models/mmi/mmt_eta_model';
import { CityCoordinatesService } from '../city-coordinates.service';

declare var google: any;

@Injectable({
  providedIn: 'root'
})
export class GoogleMapsService {

  webAPIEndPoint: string = '';
  webAPIEndPointNew: string = '';
  private googleMapsLoaded = false;
  private placesService: any;
  private directionsService: any;
  private distanceMatrixService: any;
  private geocoder: any;

  constructor(
    private httpClient: HttpClient,
    private cityCoordinatesService: CityCoordinatesService
  ) {
    this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
    this.webAPIEndPointNew = AppConfig.CabYaari_WebAPI_New;
    this.initializeGoogleMapsServices();
  }

  private initializeGoogleMapsServices() {
    if (typeof google !== 'undefined' && google.maps) {
      this.googleMapsLoaded = true;
      this.placesService = new google.maps.places.PlacesService(document.createElement('div'));
      this.directionsService = new google.maps.DirectionsService();
      this.distanceMatrixService = new google.maps.DistanceMatrixService();
      this.geocoder = new google.maps.Geocoder();
    } else {
      // Wait for Google Maps to load
      setTimeout(() => this.initializeGoogleMapsServices(), 100);
    }
  }

  // Keep existing backend API methods that don't depend on MapMyIndia
  getCarFeatures(): Observable<Array<CarCategory>> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/carcategoryfeaturesrate';
    return this.httpClient.get<Array<CarCategory>>(apiURL).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    );
  }

  getCarCategoryFare(pickUpAddressLongLat: string, dropOffAddressLongLat: string, tripType: string): Observable<any> {
    let apiURL = this.webAPIEndPointNew + '/api/v1/Booking/GetFareDetails?pickUpAddressLongLat=' + pickUpAddressLongLat + '&dropOffAddressLongLat=' + dropOffAddressLongLat + '&tripType=' + tripType;
    return this.httpClient.get<any>(apiURL).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    );
  }

  geCities(): Observable<Array<City>> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/cities';
    return this.httpClient.get<Array<City>>(apiURL).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    );
  }

  // Google Maps Places Autocomplete with city filtering
  autoSuggest(placeName: string, cityName?: string): Observable<any> {
    return new Observable(observer => {
      if (!this.googleMapsLoaded) {
        observer.error('Google Maps not loaded');
        return;
      }

      const request: any = {
        input: placeName,
        componentRestrictions: { country: 'IN' }, // Restrict to India
        types: ['establishment', 'geocode']
      };

      // If city is provided, enhance the search
      if (cityName) {
        // Add city name to the search query for better results
        request.input = `${placeName} ${cityName}`;

        // Add location bias if we have city coordinates
        const cityCoords = this.cityCoordinatesService.getCityCoordinates(cityName);
        if (cityCoords) {
          request.locationBias = {
            center: { lat: cityCoords.lat, lng: cityCoords.lng },
            radius: 50000 // 50km radius
          };
        }
      }

      const autocompleteService = new google.maps.places.AutocompleteService();
      autocompleteService.getPlacePredictions(request, (predictions: any, status: any) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
          let filteredPredictions = predictions;

          // If city is specified, filter results to prioritize those containing the city name
          if (cityName) {
            const cityLower = cityName.toLowerCase();

            // First, get exact matches for the city
            const exactMatches = predictions.filter((prediction: any) =>
              prediction.description.toLowerCase().includes(cityLower)
            );

            // If we have exact matches, use them; otherwise, use all results
            filteredPredictions = exactMatches.length > 0 ? exactMatches : predictions;
          }

          const formattedResults = {
            suggestedLocations: filteredPredictions.slice(0, 5).map((prediction: any) => ({
              placeName: prediction.structured_formatting?.main_text || prediction.description,
              placeAddress: prediction.description,
              latitude: null, // Will be filled by geocoding if needed
              longitude: null,
              placeId: prediction.place_id
            }))
          };
          observer.next(formattedResults);
        } else {
          observer.next({ suggestedLocations: [] });
        }
        observer.complete();
      });
    });
  }

  // Google Maps Geocoding
  geocodeByPlaceId(placeId: string): Observable<any> {
    return new Observable(observer => {
      if (!this.googleMapsLoaded) {
        observer.error('Google Maps not loaded');
        return;
      }

      this.geocoder.geocode({ placeId: placeId }, (results: any, status: any) => {
        if (status === google.maps.GeocoderStatus.OK && results[0]) {
          const location = results[0].geometry.location;
          observer.next({
            lat: location.lat(),
            lng: location.lng(),
            formatted_address: results[0].formatted_address
          });
        } else {
          observer.error('Geocoding failed: ' + status);
        }
        observer.complete();
      });
    });
  }

  // Google Maps Distance Matrix for ETA calculation
  getETA(srcLat: string, srcLng: string, dstLat: string, dstLng: string): Observable<ETA> {
    return new Observable(observer => {
      if (!this.googleMapsLoaded) {
        observer.error('Google Maps not loaded');
        return;
      }

      const origin = new google.maps.LatLng(parseFloat(srcLat), parseFloat(srcLng));
      const destination = new google.maps.LatLng(parseFloat(dstLat), parseFloat(dstLng));

      // First get distance matrix for time and distance
      this.distanceMatrixService.getDistanceMatrix({
        origins: [origin],
        destinations: [destination],
        travelMode: google.maps.TravelMode.DRIVING,
        unitSystem: google.maps.UnitSystem.METRIC,
        avoidHighways: false,
        avoidTolls: false
      }, (response: any, status: any) => {
        if (status === google.maps.DistanceMatrixStatus.OK) {
          const element = response.rows[0].elements[0];
          if (element.status === 'OK') {
            // Get detailed route using Directions Service
            this.directionsService.route({
              origin: origin,
              destination: destination,
              travelMode: google.maps.TravelMode.DRIVING
            }, (directionsResult: any, directionsStatus: any) => {
              if (directionsStatus === google.maps.DirectionsStatus.OK) {
                const route = directionsResult.routes[0];
                const leg = route.legs[0];

                // Format response to match existing ETA model structure
                const etaResponse: ETA = {
                  code: 'Ok',
                  Server: 'Google Maps',
                  routes: [{
                    legs: [{
                      steps: [], // Empty for now, not used in the frontend
                      weight: element.duration.value,
                      distance: element.distance.value,
                      summary: 0,
                      duration: element.duration.value
                    }],
                    weight_name: 'duration',
                    geometry: route.overview_polyline.points, // encoded polyline string
                    weight: element.duration.value,
                    distance: element.distance.value, // in meters
                    duration: element.duration.value // in seconds
                  }],
                  waypoints: []
                };
                observer.next(etaResponse);
              } else {
                observer.error('Directions request failed: ' + directionsStatus);
              }
              observer.complete();
            });
          } else {
            observer.error('Distance matrix element status: ' + element.status);
            observer.complete();
          }
        } else {
          observer.error('Distance matrix request failed: ' + status);
          observer.complete();
        }
      });
    });
  }

  // Google Maps Reverse Geocoding
  reverseGeocode(lat: number, lng: number): Observable<any> {
    return new Observable(observer => {
      if (!this.googleMapsLoaded) {
        observer.error('Google Maps not loaded');
        return;
      }

      const latlng = new google.maps.LatLng(lat, lng);
      this.geocoder.geocode({ location: latlng }, (results: any, status: any) => {
        if (status === google.maps.GeocoderStatus.OK && results[0]) {
          // Extract city from address components
          let city = '';
          let state = '';
          let country = '';
          
          for (const component of results[0].address_components) {
            if (component.types.includes('locality')) {
              city = component.long_name;
            } else if (component.types.includes('administrative_area_level_1')) {
              state = component.long_name;
            } else if (component.types.includes('country')) {
              country = component.long_name;
            }
          }

          observer.next({
            city: city,
            state: state,
            country: country,
            formatted_address: results[0].formatted_address,
            results: results
          });
        } else {
          observer.error('Reverse geocoding failed: ' + status);
        }
        observer.complete();
      });
    });
  }

  // Text search using Google Places
  textsearch(placeName: string): Observable<any> {
    return new Observable(observer => {
      if (!this.googleMapsLoaded) {
        observer.error('Google Maps not loaded');
        return;
      }

      const request = {
        query: placeName,
        fields: ['name', 'geometry', 'formatted_address', 'place_id']
      };

      this.placesService.textSearch(request, (results: any, status: any) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          const formattedResults = {
            suggestedLocations: results.map((place: any) => ({
              placeName: place.name,
              placeAddress: place.formatted_address,
              latitude: place.geometry.location.lat(),
              longitude: place.geometry.location.lng(),
              placeId: place.place_id
            }))
          };
          observer.next(formattedResults);
        } else {
          observer.next({ suggestedLocations: [] });
        }
        observer.complete();
      });
    });
  }
}
