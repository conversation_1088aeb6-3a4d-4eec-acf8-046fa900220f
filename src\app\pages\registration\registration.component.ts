import { Component, OnInit } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { ToastrService } from 'ngx-toastr';
import { AppConstants } from 'src/app/shared/constants/AppConstants';
import { ValidationMessages, ErrorMessage, SuccessMessage } from 'src/app/shared/constants/message.content';
import { UserRegistration } from 'src/app/shared/models/user-registration.model';
import { PhoneRegistrationRequest } from 'src/app/shared/models/phone-registration-request.model';
import { PhoneRegistrationResponse } from 'src/app/shared/models/phone-registration-response.model';
import { AuthService } from 'src/app/shared/services';
import { Router, ActivatedRoute } from '@angular/router';
import { ProgressService } from 'src/app/shared/services/progress.service';
import { RegistrationResponse } from 'src/app/shared/models/registration-response.model';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.css']
})
export class RegistrationComponent implements OnInit {

  constructor(
    private toastrService: ToastrService,
    private authService: AuthService,
    private progresService: ProgressService,
    private router: Router,
    private route: ActivatedRoute) { }

  readonly REGISTERATION_HEADER = 'Register with Cabyaari';
  imageFolderPath: string = AppConfig.imageFolderPath;

  userRegistration: UserRegistration;
  returnUrl: string = '';

  ngOnInit() {
    this.initFields();

    // Get returnUrl from query params if it exists
    const queryParams = this.route.snapshot.queryParams;
    this.returnUrl = queryParams['returnUrl'] || '';
    console.log('returnUrl in registration component', this.returnUrl);

    this.authService.user$.subscribe((user) => {
      console.log('subscribe user in registration component', user);
      const accessToken = localStorage.getItem('access_token');
      console.log('accessToken', accessToken);
      // const refreshToken = localStorage.getItem('refresh_token');
      if (user && accessToken /*&& refreshToken*/) {
        console.log('navigate from registration page back to user profile');
        this.router.navigate(['/userprofile']);
      }
    });
    this.authService.user$
  }

  private initFields = () => {
    this.userRegistration = new UserRegistration();
  }

  validateAndSubmit = () => {
    console.log('validateAndSubmit() called');

    const flag = this.isValid();
    if (!flag) {
      console.log('validation failed so will not call sign up API');
      return;
    }

    console.log('All Validation passed. Will call signup API now');
    this.callSignupAPI();
  }

  private isValid = (): boolean => {
    console.log('userRegistration', this.userRegistration);

    const firstName = this.userRegistration.firstName;
    console.log('firstName', firstName);
    if (!firstName || firstName.trim().length === 0) {
      console.log('failed at firstName');
      this.showToastrError(AppConstants.EMPTY_STRING, ValidationMessages.ENTER_FIRST_NAME);
      return false;
    }

    const lastName = this.userRegistration.lastName;
    console.log('lastName', lastName);
    if (!lastName || lastName.trim().length === 0) {
      console.log('failed at lastName');
      this.showToastrError(AppConstants.EMPTY_STRING, ValidationMessages.ENTER_LAST_NAME);
      return false;
    }

    const phoneNumber = this.userRegistration.phoneNumber;
    console.log('phoneNumber', phoneNumber);
    if (!phoneNumber || phoneNumber.trim().length === 0) {
      console.log('failed at phoneNumber');
      this.showToastrError(AppConstants.EMPTY_STRING, ValidationMessages.ENTER_PHONE);
      return false;
    }
    if (phoneNumber.trim().length != 10) {
      console.log('failed at phoneNumber');
      this.showToastrError(AppConstants.EMPTY_STRING, ValidationMessages.CONTACT_LENGTH);
      return false;
    }

    console.log('All validations passed!');
    return true;
  }

  private callSignupAPI = () => {
    console.log('callSignupAPI() called');

    const phoneRegistrationRequest: PhoneRegistrationRequest = {
      firstName: this.userRegistration.firstName,
      lastName: this.userRegistration.lastName,
      phoneNumber: this.userRegistration.phoneNumber
    };

    this.showProgressBar();
    this.authService.registerWithPhone(phoneRegistrationRequest).subscribe(

      /** Type should not be any but a generic model */
      (response: PhoneRegistrationResponse) => {
        this.hideProgressBar();
        console.log('response', response);

        if (response.succeeded && response.data.otpSent) {
          this.displayRegistrationSuccessMesage();
          // Navigate to OTP verification with phone number
          this.router.navigate(['/otp-verification'], {
            queryParams: {
              phoneNumber: this.userRegistration.phoneNumber,
              type: 'registration',
              returnUrl: this.returnUrl
            }
          });
        } else {
          // Handle the case where API returns Succeeded: false with a message
          if (response.message) {
            this.showToastrError(ErrorMessage.REGISTRATION_FAILED, response.message);
          } else {
            this.displayRegistrationFailedMesage();
          }
        }
      },
      (error) => {
        this.hideProgressBar();
        console.log('error', error);

        // Check for validation errors in the format { errors: { fieldName: [messages] } }
        if (error && error.errors) {
          this.displayApiValidationErrors(error.errors);
        }
        // Check for error response in the format { Succeeded: false, Message: "..." }
        else if (error.Message) {
          this.showToastrError(ErrorMessage.REGISTRATION_FAILED, error.Message);
        }
        // Fallback to generic error message
        else {
          this.displaySignupError();
        }
      }
    );
  }

  private displayApiValidationErrors = (errors: any) => {
    // Process all validation errors
    for (const field in errors) {
      if (errors.hasOwnProperty(field)) {
        const errorMessages = errors[field];
        if (Array.isArray(errorMessages) && errorMessages.length > 0) {
          // Display the first error message for each field
          this.showToastrError(`${field} Error`, errorMessages[0]);
          return; // Show only the first error to avoid multiple toasts
        }
      }
    }
  }

  private displaySignupError = () => {
    this.showToastrError(ErrorMessage.REGISTRATION_NOT_WORKING, ErrorMessage.TRY_AFTER_SOME_TIME);
  }

  private displayRegistrationFailedMesage = () => {
    this.showToastrError(ErrorMessage.REGISTRATION_FAILED, AppConstants.EMPTY_STRING);
  }

  private displayRegistrationSuccessMesage = () => {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.REGISTRATION_SUCCESSFULL);
  }

  private navigateLoginPage = () => {
    // If returnUrl exists, pass it to the login page
    if (this.returnUrl && this.returnUrl.trim().length > 0) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: this.returnUrl } });
    } else {
      this.router.navigate(['/login']);
    }
  }

  private showToastrError = (title: string, text: string) => {
    this.toastrService.error(text, title);
  }

  private showProgressBar = () => {
    console.log('showProgressBar() called');
    this.progresService.isPorgress.next(true);
  }

  private hideProgressBar = () => {
    console.log('hideProgressBar() called');
    this.progresService.isPorgress.next(false);
  }
}
