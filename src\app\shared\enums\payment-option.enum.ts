/**
 * Centralized payment option enum to ensure consistency across the application
 * This enum defines the standard payment option values used throughout the system
 */
export enum PaymentOption {
  /**
   * Partial payment - user pays a portion online, rest to driver
   */
  PARTIAL = 1,
  
  /**
   * Full payment - user pays the complete amount online
   */
  FULL = 2
}

/**
 * Utility class for payment option operations
 */
export class PaymentOptionUtil {
  
  /**
   * Check if the payment option is partial payment
   * @param paymentOption - The payment option value
   * @returns True if it's partial payment
   */
  static isPartialPayment(paymentOption: number): boolean {
    return paymentOption === PaymentOption.PARTIAL;
  }
  
  /**
   * Check if the payment option is full payment
   * @param paymentOption - The payment option value
   * @returns True if it's full payment
   */
  static isFullPayment(paymentOption: number): boolean {
    return paymentOption === PaymentOption.FULL;
  }
  
  /**
   * Get payment type string representation
   * @param paymentOption - The payment option value
   * @returns String representation of payment type
   */
  static getPaymentTypeString(paymentOption: number): string {
    switch (paymentOption) {
      case PaymentOption.PARTIAL:
        return 'PARTIAL';
      case PaymentOption.FULL:
        return 'FULL';
      default:
        return 'UNKNOWN';
    }
  }
}
