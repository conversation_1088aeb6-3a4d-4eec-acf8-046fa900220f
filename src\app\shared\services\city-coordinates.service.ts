import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CityCoordinatesService {

  // Major Indian cities coordinates for location bias
  private cityCoordinates: { [key: string]: { lat: number, lng: number } } = {
    'Delhi': { lat: 28.6139, lng: 77.2090 },
    'Mumbai': { lat: 19.0760, lng: 72.8777 },
    'Bangalore': { lat: 12.9716, lng: 77.5946 },
    'Bengaluru': { lat: 12.9716, lng: 77.5946 },
    'Hyderabad': { lat: 17.3850, lng: 78.4867 },
    'Chennai': { lat: 13.0827, lng: 80.2707 },
    'Kolkata': { lat: 22.5726, lng: 88.3639 },
    'Pune': { lat: 18.5204, lng: 73.8567 },
    'Ahmedabad': { lat: 23.0225, lng: 72.5714 },
    'Jaipur': { lat: 26.9124, lng: 75.7873 },
    'Surat': { lat: 21.1702, lng: 72.8311 },
    'Lucknow': { lat: 26.8467, lng: 80.9462 },
    'Kanpur': { lat: 26.4499, lng: 80.3319 },
    'Nagpur': { lat: 21.1458, lng: 79.0882 },
    'Indore': { lat: 22.7196, lng: 75.8577 },
    'Thane': { lat: 19.2183, lng: 72.9781 },
    'Bhopal': { lat: 23.2599, lng: 77.4126 },
    'Visakhapatnam': { lat: 17.6868, lng: 83.2185 },
    'Pimpri-Chinchwad': { lat: 18.6298, lng: 73.7997 },
    'Patna': { lat: 25.5941, lng: 85.1376 },
    'Vadodara': { lat: 22.3072, lng: 73.1812 },
    'Ghaziabad': { lat: 28.6692, lng: 77.4538 },
    'Ludhiana': { lat: 30.9010, lng: 75.8573 },
    'Agra': { lat: 27.1767, lng: 78.0081 },
    'Nashik': { lat: 19.9975, lng: 73.7898 },
    'Faridabad': { lat: 28.4089, lng: 77.3178 },
    'Meerut': { lat: 28.9845, lng: 77.7064 },
    'Rajkot': { lat: 22.3039, lng: 70.8022 },
    'Kalyan-Dombivali': { lat: 19.2403, lng: 73.1305 },
    'Vasai-Virar': { lat: 19.4912, lng: 72.8054 },
    'Varanasi': { lat: 25.3176, lng: 82.9739 },
    'Srinagar': { lat: 34.0837, lng: 74.7973 },
    'Aurangabad': { lat: 19.8762, lng: 75.3433 },
    'Dhanbad': { lat: 23.7957, lng: 86.4304 },
    'Amritsar': { lat: 31.6340, lng: 74.8723 },
    'Navi Mumbai': { lat: 19.0330, lng: 73.0297 },
    'Allahabad': { lat: 25.4358, lng: 81.8463 },
    'Prayagraj': { lat: 25.4358, lng: 81.8463 },
    'Ranchi': { lat: 23.3441, lng: 85.3096 },
    'Howrah': { lat: 22.5958, lng: 88.2636 },
    'Coimbatore': { lat: 11.0168, lng: 76.9558 },
    'Jabalpur': { lat: 23.1815, lng: 79.9864 },
    'Gwalior': { lat: 26.2183, lng: 78.1828 },
    'Vijayawada': { lat: 16.5062, lng: 80.6480 },
    'Jodhpur': { lat: 26.2389, lng: 73.0243 },
    'Madurai': { lat: 9.9252, lng: 78.1198 },
    'Raipur': { lat: 21.2514, lng: 81.6296 },
    'Kota': { lat: 25.2138, lng: 75.8648 },
    'Chandigarh': { lat: 30.7333, lng: 76.7794 },
    'Guwahati': { lat: 26.1445, lng: 91.7362 },
    'Solapur': { lat: 17.6599, lng: 75.9064 },
    'Hubli-Dharwad': { lat: 15.3647, lng: 75.1240 },
    'Bareilly': { lat: 28.3670, lng: 79.4304 },
    'Moradabad': { lat: 28.8386, lng: 78.7733 },
    'Mysore': { lat: 12.2958, lng: 76.6394 },
    'Mysuru': { lat: 12.2958, lng: 76.6394 },
    'Gurgaon': { lat: 28.4595, lng: 77.0266 },
    'Gurugram': { lat: 28.4595, lng: 77.0266 },
    'Aligarh': { lat: 27.8974, lng: 78.0880 },
    'Jalandhar': { lat: 31.3260, lng: 75.5762 },
    'Tiruchirappalli': { lat: 10.7905, lng: 78.7047 },
    'Bhubaneswar': { lat: 20.2961, lng: 85.8245 },
    'Salem': { lat: 11.6643, lng: 78.1460 },
    'Warangal': { lat: 17.9689, lng: 79.5941 },
    'Mira-Bhayandar': { lat: 19.2952, lng: 72.8544 },
    'Thiruvananthapuram': { lat: 8.5241, lng: 76.9366 },
    'Bhiwandi': { lat: 19.3002, lng: 73.0635 },
    'Saharanpur': { lat: 29.9680, lng: 77.5552 },
    'Guntur': { lat: 16.3067, lng: 80.4365 },
    'Amravati': { lat: 20.9374, lng: 77.7796 },
    'Bikaner': { lat: 28.0229, lng: 73.3119 },
    'Noida': { lat: 28.5355, lng: 77.3910 },
    'Jamshedpur': { lat: 22.8046, lng: 86.2029 },
    'Bhilai Nagar': { lat: 21.1938, lng: 81.3509 },
    'Cuttack': { lat: 20.4625, lng: 85.8828 },
    'Firozabad': { lat: 27.1592, lng: 78.3957 },
    'Kochi': { lat: 9.9312, lng: 76.2673 },
    'Bhavnagar': { lat: 21.7645, lng: 72.1519 },
    'Dehradun': { lat: 30.3165, lng: 78.0322 },
    'Durgapur': { lat: 23.5204, lng: 87.3119 },
    'Asansol': { lat: 23.6739, lng: 86.9524 },
    'Nanded': { lat: 19.1383, lng: 77.3210 },
    'Kolhapur': { lat: 16.7050, lng: 74.2433 },
    'Ajmer': { lat: 26.4499, lng: 74.6399 },
    'Akola': { lat: 20.7002, lng: 77.0082 },
    'Gulbarga': { lat: 17.3297, lng: 76.8343 },
    'Jamnagar': { lat: 22.4707, lng: 70.0577 },
    'Ujjain': { lat: 23.1765, lng: 75.7885 },
    'Loni': { lat: 28.7333, lng: 77.2833 },
    'Siliguri': { lat: 26.7271, lng: 88.3953 },
    'Jhansi': { lat: 25.4484, lng: 78.5685 },
    'Ulhasnagar': { lat: 19.2215, lng: 73.1645 },
    'Jammu': { lat: 32.7266, lng: 74.8570 },
    'Sangli-Miraj & Kupwad': { lat: 16.8524, lng: 74.5815 },
    'Mangalore': { lat: 12.9141, lng: 74.8560 },
    'Erode': { lat: 11.3410, lng: 77.7172 },
    'Belgaum': { lat: 15.8497, lng: 74.4977 },
    'Ambattur': { lat: 13.1143, lng: 80.1548 },
    'Tirunelveli': { lat: 8.7139, lng: 77.7567 },
    'Malegaon': { lat: 20.5579, lng: 74.5287 },
    'Gaya': { lat: 24.7914, lng: 85.0002 },
    'Jalgaon': { lat: 21.0077, lng: 75.5626 },
    'Udaipur': { lat: 24.5854, lng: 73.7125 },
    'Maheshtala': { lat: 22.4986, lng: 88.2475 }
  };

  constructor() { }

  /**
   * Get coordinates for a city
   * @param cityName Name of the city
   * @returns Coordinates object with lat and lng, or null if not found
   */
  getCityCoordinates(cityName: string): { lat: number, lng: number } | null {
    if (!cityName) return null;
    
    // Try exact match first
    const exactMatch = this.cityCoordinates[cityName];
    if (exactMatch) return exactMatch;
    
    // Try case-insensitive match
    const cityKey = Object.keys(this.cityCoordinates).find(
      key => key.toLowerCase() === cityName.toLowerCase()
    );
    
    return cityKey ? this.cityCoordinates[cityKey] : null;
  }

  /**
   * Check if a city has coordinates available
   * @param cityName Name of the city
   * @returns true if coordinates are available
   */
  hasCityCoordinates(cityName: string): boolean {
    return this.getCityCoordinates(cityName) !== null;
  }

  /**
   * Get all available cities
   * @returns Array of city names
   */
  getAvailableCities(): string[] {
    return Object.keys(this.cityCoordinates);
  }
}
