<app-progress-loader></app-progress-loader>

<app-sidemenu></app-sidemenu> 

<header id="header" class="login-header">
    <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
    <div class="topheader loginheader"><a routerLink="/home" class="logo"><img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;" /></a>
       
    </div>
    <div class="loginform">
        <div class="col-12 col-12-small">
            <form method="post" action="#">
                <div class="row gtr-50">
                    <div class="col-12" *ngIf="!isPhoneLogin">
                        <h3>Log in to our website </h3>
                        <!-- <span>Login to our website</span> -->
                        <div class="inputrow">
                            <input type="text" id="userName" name="username" [(ngModel)]="username" class="form-control"
                                placeholder="Email ID" required autofocus />
                        </div>
                        <div class="inputrow">
                            <input type="password" id="inputPassword" name="password" [(ngModel)]="password"
                                class="form-control" placeholder="Password" required />

                        </div>
                        <div class="checkbox mb-3 text-danger" *ngIf="loginError">
                            Login failed. Please try again.
                        </div>
                        <div class="login" (click)="login()">Log in</div>
                        <div class="bottombtn">
                            <a>Forgot Password?</a>
                            <a [routerLink]="['/registration']" [queryParams]="{ returnUrl: returnUrl }">Register Now</a>
                            <a (click)="isPhoneLogin = true">Login with Phone</a>
                        </div>
                    </div>
    
                    <div class="searator" *ngIf="!isPhoneLogin">
                        <div class="searatorline"></div>
                        <span class="or">OR</span>
                        <div class="searatorline"></div>
                    </div>

                    <div class="col-12 otprow" *ngIf="isPhoneLogin">
                        <h3>Login with Phone</h3>
                        <div class="inputrow">
                            <input type="text" name="phoneNumber" [(ngModel)]="phoneNumber"
                                class="form-control" placeholder="Phone Number" required autofocus />
                        </div>
                        <div class="checkbox mb-3 text-danger" *ngIf="loginError">
                            Login failed. Please try again.
                        </div>
                        <div class="login" (click)="sendOTPAndVerify()">Continue to Verify</div>
                        <div class="bottombtn">
                            <a [routerLink]="['/registration']" [queryParams]="{ returnUrl: returnUrl }">New User? Sign Up</a>
                            <a (click)="isPhoneLogin = false">Login with Email</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</header>

<div id="main" class="loginpage d-none d-lg-block col-lg-12">
    <section id="two" class="logincontent"></section>
</div>