import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription, Observable, BehaviorSubject, of, throwError, from } from 'rxjs';
import { Router } from '@angular/router';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { map, delay, tap, finalize, catchError } from 'rxjs/operators';
import { AppConfig } from 'src/app/configs/app.config';
import { ApplicationUser } from '../models/application-user';
import { LoginResult } from '../models/login-result';
import { LoginRequest } from '../models/login-request';
import { UserRegistration } from '../models/user-registration.model';
import { RegistrationResponse } from '../models/registration-response.model';
import { PhoneRegistrationRequest } from '../models/phone-registration-request.model';
import { PhoneRegistrationResponse } from '../models/phone-registration-response.model';
import { OtpVerificationRequest } from '../models/otp-verification-request.model';
import { OtpVerificationResponse } from '../models/otp-verification-response.model';
import { PhoneLoginRequest } from '../models/phone-login-request.model';
import { PhoneLoginResponse } from '../models/phone-login-response.model';
import { LoggerService } from '../interceptors/logger.service';
import { ProgressService } from './progress.service';
import axios from 'axios';

@Injectable({
  providedIn: 'root'
})
export class AuthService implements OnDestroy {
  constructor(
    private router: Router, 
    private http: HttpClient,
    private progresService: ProgressService) {
    this.logger = LoggerService.createLogger('AuthService');

    window.addEventListener('storage', this.storageEventListener.bind(this));

    const userJson = localStorage.getItem('currentUser');
    this.logger.trace('userJson', userJson);
    if (userJson && userJson.trim().length > 0) {
      const currentUser: ApplicationUser = JSON.parse(userJson);
      this.logger.trace('currentUser', currentUser);
      this._user.next(currentUser);
      this.user$ = of(this._user.value);
    }
  }

  private logger: LoggerService;

  webAPIEndPoint = AppConfig.CabYaari_WebAPI_New;
  
  private readonly apiUrl = `${this.webAPIEndPoint}`;
  private timer: Subscription;
  private _user = new BehaviorSubject<ApplicationUser>(null);
  user$: Observable<ApplicationUser> = this._user.asObservable();

  private storageEventListener(event: StorageEvent) {
    this.logger.trace('storageEventListener() called event', event);
    if (event.storageArea === localStorage) {
      if (event.key === 'logout-event') {
        this.stopTokenTimer();
        this._user.next(null);
        this.user$ = of(this._user.value);
      }
      if (event.key === 'login-event') {
        this.stopTokenTimer();
        this.http.get<LoginResult>(`${this.apiUrl}/user`).subscribe((x) => {
          const appUser: ApplicationUser = {
            role: x.role,
            isVerified: x.isVerified,
            username: x.username,
            email: x.email,
            jwToken: x.jwToken,
            firstName: x.firstName,
            lastName: x.lastName,
            phoneNumber: x.phoneNumber
          }

          this.setLocalStorage(appUser);
          this._user.next(appUser);
          this.user$ = of(this._user.value);
        });
      }
    }
  }

  ngOnDestroy(): void {
    window.removeEventListener('storage', this.storageEventListener.bind(this));
  }

  dummyLogin = (loginRequest: LoginRequest): Observable<LoginResult> => {
    this.logger.trace('dummyLogin() called with loginRequest', loginRequest);

    const username = loginRequest.email;
    const password = loginRequest.password;
    this.logger.trace('username', username, 'password', password);

    if (username === '<EMAIL>' && password === 'admin123') {
      this.logger.trace('valid dummy request');
      const result: LoginResult = new LoginResult();
      result.jwToken = 'aasvaascacsddsadcacsca';
      result.username = 'admin';
      result.refreshToken = 'afasdascadcascasas';
      result.email = '<EMAIL>';

      const appUser: ApplicationUser = {
        role: result.role,
        isVerified: result.isVerified,
        username: result.username,
        email:result.email,
        jwToken:result.jwToken,
        firstName: result.firstName,
        lastName: result.lastName,
        phoneNumber: result.phoneNumber
      }

      this.setLocalStorage(appUser);
      this._user.next(appUser);
      this.user$ = of(this._user.value);
      localStorage.setItem('currentUser', JSON.stringify(appUser));
      return of(result);
    } else {
      this.logger.trace('Invalid dummy request');
      return throwError('error');
    }
  }

  login(loginRequest:LoginRequest): Observable<LoginResult> {
    
    let loginApi = this.apiUrl + '/api/Account/authenticate';

    return this.http.post<any>(
        loginApi, loginRequest
      ).pipe(
        map(user => {
          // store user details and jwt token in local storage to keep user logged in between page refreshes
          const appUser: ApplicationUser = {
            role: user.data['roles'],
            isVerified: user.data['isVerified'],
            username: user.data['userName'],
            email:user.data['email'],
            jwToken:user.data['jwToken'],
            firstName: user.data['firstName'],
            lastName: user.data['lastName'],
            phoneNumber: user.data['phoneNumber']
          }
          this.logger.trace('appUser', appUser);
          localStorage.setItem('currentUser', JSON.stringify(appUser));
          this.setLocalStorage(appUser);
          this._user.next(appUser);
          this.user$ = of(this._user.value);
          return user;
      })
      );

    // return this.http
    // .post<LoginResult>(loginApi, { username, password)
    //   .pipe(
    //     map((x) => {
    //       this._user.next({
    //         username: x.username,
    //         role: x.role,
    //         originalUserName: x.originalUserName,
    //       });
    //       this.setLocalStorage(x);
    //       this.startTokenTimer();
    //       return x;
    //     })
    //   );
  }

  logout = () => {
    this.showProgressBar();
    this.http
      .post<unknown>(`${this.apiUrl}/logout`, {})
      .pipe(
        finalize(() => {
          this.hideProgressBar();
          this.clearLocalStorage();
          localStorage.removeItem('currentUser');
          this._user.next(null);
          this.user$ = of(this._user.value);
          this.stopTokenTimer();
          this.router.navigate(['login']);
        })
      )
      .subscribe();
  }

  private showProgressBar = () => {
    this.logger.trace('showProgressBar() called');
    this.progresService.isPorgress.next(true);
  }

  private hideProgressBar = () => {
    this.logger.trace('hideProgressBar() called');
    this.progresService.isPorgress.next(false);
  }

  getCurrentUser(){
    return JSON.parse(localStorage.getItem('currentUser'));
  }

  register = (request: UserRegistration): Observable<RegistrationResponse> => {
    this.logger.trace('register called with request', request);
    const url = this.apiUrl + '/api/Account/register';
    this.logger.trace('url', url);

    // Using Axios instead of HttpClient
    return from(axios.post(url, request))
      .pipe(
        map(response => response.data),
        catchError((error) => {
          const apiError = error.response?.data;
          if (typeof apiError === 'object' && apiError !== null) {
            console.log('Error message from API:', apiError.Message);
            return throwError(apiError);
          } else {
            console.log('Raw error response:', apiError);
          }

          this.logger.error('Registration error:', error);
          // Re-throw the error to be handled by the component
          return throwError(error);
        })
      );
  }

  // Phone-based registration
  registerWithPhone = (request: PhoneRegistrationRequest): Observable<PhoneRegistrationResponse> => {
    this.logger.trace('registerWithPhone called with request', request);
    const url = this.apiUrl + '/api/account/register-phone';
    this.logger.trace('url', url);

    return from(axios.post(url, request))
      .pipe(
        map(response => response.data),
        catchError((error) => {
          const apiError = error.response?.data;
          if (typeof apiError === 'object' && apiError !== null) {
            console.log('Error message from API:', apiError.Message);
            return throwError(apiError);
          } else {
            console.log('Raw error response:', apiError);
          }

          this.logger.error('Phone registration error:', error);
          return throwError(error);
        })
      );
  }

  // OTP verification
  verifyOtp = (request: OtpVerificationRequest): Observable<OtpVerificationResponse> => {
    this.logger.trace('verifyOtp called with request', request);
    const url = this.apiUrl + '/api/account/verify-otp';
    this.logger.trace('url', url);

    return from(axios.post(url, request))
      .pipe(
        map(response => {
          const otpResponse = response.data;

          // If verification is successful, set up user session
          if (otpResponse.succeeded && otpResponse.data) {
            const appUser: ApplicationUser = {
              role: otpResponse.data.roles,
              isVerified: otpResponse.data.isVerified,
              username: otpResponse.data.userName,
              email: otpResponse.data.email,
              jwToken: otpResponse.data.jwToken,
              firstName: otpResponse.data.firstName,
              lastName: otpResponse.data.lastName,
              phoneNumber: otpResponse.data.phoneNumber
            };

            this.logger.trace('appUser from OTP verification', appUser);
            localStorage.setItem('currentUser', JSON.stringify(appUser));
            this.setLocalStorage(appUser);
            this._user.next(appUser);
            this.user$ = of(this._user.value);
          }

          return otpResponse;
        }),
        catchError((error) => {
          const apiError = error.response?.data;
          if (typeof apiError === 'object' && apiError !== null) {
            console.log('Error message from API:', apiError.Message);
            return throwError(apiError);
          } else {
            console.log('Raw error response:', apiError);
          }

          this.logger.error('OTP verification error:', error);
          return throwError(error);
        })
      );
  }

  // Phone-based login
  loginWithPhone = (request: PhoneLoginRequest): Observable<PhoneLoginResponse> => {
    this.logger.trace('loginWithPhone called with request', request);
    const url = this.apiUrl + '/api/account/login-phone';
    this.logger.trace('url', url);

    return from(axios.post(url, request))
      .pipe(
        map(response => response.data),
        catchError((error) => {
          const apiError = error.response?.data;
          if (typeof apiError === 'object' && apiError !== null) {
            console.log('Error message from API:', apiError.Message);
            return throwError(apiError);
          } else {
            console.log('Raw error response:', apiError);
          }

          this.logger.error('Phone login error:', error);
          return throwError(error);
        })
      );
  }

  refreshToken() {
    this.logger.trace('refreshToken() called');
    const refreshToken = localStorage.getItem('refresh_token');
    /* if (!refreshToken) {
      this.clearLocalStorage();
      return of(null);
    } */

    return this.http
      .post<LoginResult>(`${this.apiUrl}/refresh-token`, { refreshToken })
      .pipe(
        map((x) => {
          const appUser: ApplicationUser = {
            username: x.username,
            role: x.role,
            email: x.email,
            isVerified:x.isVerified,
            jwToken:x.jwToken,
            firstName: x.firstName,
            lastName: x.lastName,
            phoneNumber: x.phoneNumber
          };

          this._user.next(appUser);
          this.user$ = of(appUser);
          this.setLocalStorage(appUser);
          this.setRefreshToken(x);
          this.startTokenTimer();
          return x;
        })
      );
  }

  setRefreshToken = (x: LoginResult) => {
    this.logger.trace('setRefreshToken() called with x', x);
    localStorage.setItem('refresh_token', x.refreshToken);
  }

  setLocalStorage = (user: ApplicationUser) => {
    this.logger.trace('setLocalStorage() called with user', user);
    localStorage.setItem('access_token', user.jwToken);
    localStorage.setItem('login-event', 'login' + Math.random());
  }

  clearLocalStorage() {
    this.logger.trace('clearLocalStorage() called');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.setItem('logout-event', 'logout' + Math.random());
  }

  private getTokenRemainingTime() {
    const accessToken = localStorage.getItem('access_token');
    if (!accessToken) {
      return 0;
    }
    const jwtToken = JSON.parse(atob(accessToken.split('.')[1]));
    const expires = new Date(jwtToken.exp * 1000);
    return expires.getTime() - Date.now();
  }

  private startTokenTimer() {
    const timeout = this.getTokenRemainingTime();
    this.timer = of(true)
      .pipe(
        delay(timeout),
        tap(() => this.refreshToken().subscribe())
      )
      .subscribe();
  }

  private stopTokenTimer() {
    this.timer?.unsubscribe();
  }
}
