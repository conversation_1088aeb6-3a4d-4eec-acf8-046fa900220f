export interface UserBooking {
  bookingId: string;
  pickUpCity: string;
  dropOffCity: string;
  tripType: string;
  carCategory: string;
  fare: number;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpDate: string;
  pickUpTime: string;
  travelerName: string;
  phoneNumber: string;
  razorpayStatus: string;
  bookingDate: string | null;
  isPartialPayment: boolean;
  paidAmount: number;
}

export interface UserBookingsData {
  bookings: UserBooking[];
  nextCursor: string | null;
  hasMoreResult: boolean;
  totalCount: number;
}

export interface UserBookingsResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: UserBookingsData;
}

export interface UserBookingsRequest {
  pageSize?: number;
  cursor?: string;
}
